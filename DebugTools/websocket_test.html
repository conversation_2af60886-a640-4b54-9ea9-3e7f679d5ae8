<!DOCTYPE html>
<html>
<head>
    <title>WebSocket流媒体测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        canvas { border: 1px solid #ccc; max-width: 100%; }
        button { padding: 8px 16px; margin: 5px; }
        .log { background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket流媒体测试</h1>
        
        <div>
            <button onclick="connectWebSocket()">连接WebSocket</button>
            <button onclick="disconnectWebSocket()">断开连接</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status" class="status info">未连接</div>
        
        <canvas id="canvas" width="400" height="600"></canvas>
        
        <div>
            <h3>日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let frameCount = 0;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function connectWebSocket() {
            if (ws) {
                log('WebSocket已连接');
                return;
            }
            
            const wsUrl = 'ws://localhost:8000/api/streaming/stream';
            log(`连接WebSocket: ${wsUrl}`);
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                setStatus('WebSocket已连接', 'success');
                log('WebSocket连接成功');
            };
            
            ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    log(`收到消息: ${message.type}`);
                    
                    if (message.type === 'frame') {
                        drawFrame(message);
                        frameCount++;
                        if (frameCount % 10 === 0) {
                            log(`已接收 ${frameCount} 帧`);
                        }
                    } else if (message.type === 'connection') {
                        log(`连接确认: ${message.message}`);
                    }
                } catch (error) {
                    log(`解析消息失败: ${error.message}`);
                }
            };
            
            ws.onclose = () => {
                setStatus('WebSocket已断开', 'error');
                log('WebSocket连接关闭');
                ws = null;
            };
            
            ws.onerror = (error) => {
                setStatus('WebSocket错误', 'error');
                log(`WebSocket错误: ${error}`);
            };
        }
        
        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                setStatus('已断开连接', 'info');
                log('主动断开WebSocket连接');
            }
        }
        
        function drawFrame(frameData) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            img.onload = () => {
                // 计算适合的尺寸
                const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
                const width = img.width * scale;
                const height = img.height * scale;
                const x = (canvas.width - width) / 2;
                const y = (canvas.height - height) / 2;
                
                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 绘制图片
                ctx.drawImage(img, x, y, width, height);
                
                log(`绘制帧: ${img.width}x${img.height} -> ${Math.round(width)}x${Math.round(height)}`);
            };
            
            img.onerror = () => {
                log('图片加载失败');
            };
            
            const format = frameData.format || 'png';
            img.src = `data:image/${format};base64,${frameData.data}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动连接
        window.onload = () => {
            log('页面加载完成');
        };
    </script>
</body>
</html>
