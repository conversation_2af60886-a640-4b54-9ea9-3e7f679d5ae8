<!DOCTYPE html>
<html>
<head>
    <title>简单WebSocket测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: auto; margin: 10px 0; }
        button { margin: 5px; padding: 10px; }
        canvas { border: 1px solid #ccc; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>简单WebSocket流媒体测试</h1>
    
    <div>
        <button onclick="startTest()">开始测试</button>
        <button onclick="stopTest()">停止测试</button>
    </div>
    
    <canvas id="canvas" width="400" height="600"></canvas>
    
    <div class="log" id="log"></div>

    <script>
        let ws = null;
        let frameCount = 0;

        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function drawFrame(frameData) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            const img = new Image();
            img.onload = () => {
                log(`图片加载成功: ${img.width}x${img.height}`);
                
                // 清空画布
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                
                // 计算缩放
                const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
                const width = img.width * scale;
                const height = img.height * scale;
                const x = (canvas.width - width) / 2;
                const y = (canvas.height - height) / 2;
                
                // 绘制图片
                ctx.drawImage(img, x, y, width, height);
                
                frameCount++;
                log(`绘制完成，第${frameCount}帧`);
            };
            
            img.onerror = (error) => {
                log(`图片加载失败: ${error}`);
            };
            
            const format = frameData.format || 'png';
            img.src = `data:image/${format};base64,${frameData.data}`;
            log(`设置图片源: ${format}, 数据长度: ${frameData.data.length}`);
        }

        async function startTest() {
            try {
                // 1. 启动流媒体
                log('启动流媒体服务...');
                const response = await fetch('/api/streaming/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        device_id: '3ef2ce8b',
                        fps: 5,
                        quality: 50,
                        scale: 0.5
                    })
                });
                
                const data = await response.json();
                if (!data.success) {
                    throw new Error(data.message);
                }
                log('流媒体服务启动成功');
                
                // 2. 连接WebSocket
                log('连接WebSocket...');
                const wsUrl = `ws://${window.location.host}/api/streaming/stream`;
                ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    log('WebSocket连接成功');
                };
                
                ws.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        log(`收到消息: ${message.type}`);
                        
                        if (message.type === 'frame') {
                            drawFrame(message);
                        } else if (message.type === 'connection') {
                            log(`连接确认: ${message.message}`);
                        }
                    } catch (error) {
                        log(`解析消息失败: ${error.message}`);
                    }
                };
                
                ws.onclose = () => {
                    log('WebSocket连接关闭');
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket错误: ${error}`);
                };
                
            } catch (error) {
                log(`测试失败: ${error.message}`);
            }
        }

        async function stopTest() {
            if (ws) {
                ws.close();
                ws = null;
            }
            
            try {
                await fetch('/api/streaming/stop', { method: 'POST' });
                log('流媒体服务已停止');
            } catch (error) {
                log(`停止失败: ${error.message}`);
            }
        }

        window.onload = () => {
            log('页面加载完成，点击"开始测试"按钮');
        };
    </script>
</body>
</html>
