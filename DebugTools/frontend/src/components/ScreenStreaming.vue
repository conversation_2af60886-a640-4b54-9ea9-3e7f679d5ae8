<template>
  <div class="screen-streaming">
    <!-- 标题和控制面板 -->
    <div class="streaming-header">
      <h3>实时流媒体</h3>
      <div class="header-actions">
        <a-button @click="openFileManager" size="small">
          <template #icon><icon-folder /></template>
          文件管理
        </a-button>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="streaming-controls">
      <div class="control-group">
        <a-button
          v-if="!isStreaming"
          type="primary"
          @click="startStreaming"
          :loading="startingStream"
          :disabled="!deviceStore.isConnected"
          size="small"
        >
          <template #icon><icon-play-arrow /></template>
          开始实时流媒体
        </a-button>
        <a-button
          v-else
          status="danger"
          @click="stopStreaming"
          :loading="stoppingStream"
          size="small"
        >
          <template #icon><icon-pause /></template>
          停止流媒体
        </a-button>

        <a-button
          v-if="!isRecording"
          @click="startRecording"
          :disabled="!isStreaming"
          size="small"
        >
          <template #icon><icon-record /></template>
          开始录制
        </a-button>
        <a-button
          v-else
          status="warning"
          @click="stopRecording"
          :loading="stoppingRecord"
          size="small"
        >
          <template #icon><icon-record-stop /></template>
          停止录制
        </a-button>

        <a-button
          @click="takeScreenshot"
          :disabled="!isStreaming"
          :loading="takingScreenshot"
          size="small"
        >
          <template #icon><icon-camera /></template>
          截图
        </a-button>
      </div>

      <div class="status-group">
        <a-tag :color="connectionStatus === 'connected' ? 'green' : 'red'" size="small">
          {{ connectionStatusText }}
        </a-tag>
        <span v-if="isStreaming" class="fps-display">FPS: {{ currentFps }}</span>
      </div>
    </div>

    <!-- 设置面板 -->
    <div class="streaming-settings">
      <a-row :gutter="8">
        <a-col :span="6">
          <a-form-item label="帧率" size="small">
            <a-select v-model="settings.fps" size="small" :disabled="isStreaming">
              <a-option :value="5">5 FPS</a-option>
              <a-option :value="10">10 FPS</a-option>
              <a-option :value="15">15 FPS</a-option>
              <a-option :value="20">20 FPS</a-option>
              <a-option :value="30">30 FPS</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="质量" size="small">
            <a-select v-model="settings.quality" size="small" :disabled="isStreaming">
              <a-option :value="60">低质量</a-option>
              <a-option :value="80">中质量</a-option>
              <a-option :value="90">高质量</a-option>
              <a-option :value="100">最高质量</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="缩放" size="small">
            <a-select v-model="settings.scale" size="small" :disabled="isStreaming">
              <a-option :value="0.3">30%</a-option>
              <a-option :value="0.5">50%</a-option>
              <a-option :value="0.7">70%</a-option>
              <a-option :value="1.0">100%</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="录制格式" size="small">
            <a-select v-model="recordingFormat" size="small">
              <a-option value="webm">WebM</a-option>
              <a-option value="mp4">MP4 (服务端)</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
    </div>

    <!-- 流媒体显示区域 -->
    <div class="streaming-display">
      <div class="stream-container" ref="streamContainer">
        <canvas
          ref="streamCanvas"
          class="stream-canvas"
          @click="onCanvasClick"
        ></canvas>
        
        <div v-if="!isStreaming" class="no-stream">
          <a-empty description="点击开始实时流媒体查看设备屏幕" />
        </div>
        
        <div v-if="isStreaming && !hasFrame" class="loading-stream">
          <a-spin size="large" />
          <p>正在连接流媒体...</p>
        </div>
      </div>
    </div>

    <!-- 录制和截图管理 -->
    <div class="media-management">
      <a-tabs size="small">
        <a-tab-pane key="recordings" title="录制文件">
          <div class="recordings-list">
            <a-list :data="recordings" size="small">
              <template #item="{ item }">
                <a-list-item>
                  <div class="recording-info">
                    <span class="filename">{{ item.filename }}</span>
                    <span class="filesize">{{ formatFileSize(item.size) }}</span>
                    <span class="time">{{ formatTime(item.created_time) }}</span>
                  </div>
                  <div class="recording-actions">
                    <a-button size="mini" @click="downloadRecording(item.filename)">
                      下载
                    </a-button>
                    <a-popconfirm
                      content="确定要删除这个录制文件吗？"
                      @ok="deleteRecording(item.filename)"
                    >
                      <a-button size="mini" status="danger">删除</a-button>
                    </a-popconfirm>
                  </div>
                </a-list-item>
              </template>
              <template #empty>
                <a-empty description="暂无录制文件" />
              </template>
            </a-list>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="screenshots" title="截图文件">
          <div class="screenshots-list">
            <a-list :data="screenshots" size="small">
              <template #item="{ item }">
                <a-list-item>
                  <div class="screenshot-info">
                    <span class="filename">{{ item.filename }}</span>
                    <span class="filesize">{{ formatFileSize(item.size) }}</span>
                    <span class="time">{{ formatTime(item.created_time) }}</span>
                  </div>
                  <div class="screenshot-actions">
                    <a-button size="mini" @click="viewScreenshot(item.filename)">
                      查看
                    </a-button>
                    <a-button size="mini" @click="downloadScreenshot(item.filename)">
                      下载
                    </a-button>
                    <a-popconfirm
                      content="确定要删除这个截图吗？"
                      @ok="deleteScreenshot(item.filename)"
                    >
                      <a-button size="mini" status="danger">删除</a-button>
                    </a-popconfirm>
                  </div>
                </a-list-item>
              </template>
              <template #empty>
                <a-empty description="暂无截图文件" />
              </template>
            </a-list>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 截图查看模态框 -->
    <a-modal
      v-model:visible="showScreenshotViewer"
      :title="currentViewingScreenshot"
      width="80%"
      :footer="false"
    >
      <div class="screenshot-viewer" v-if="currentViewingScreenshot">
        <img
          :src="getScreenshotUrl(currentViewingScreenshot)"
          :alt="currentViewingScreenshot"
          style="max-width: 100%; height: auto;"
        />
      </div>
    </a-modal>

    <!-- 文件管理模态框 -->
    <StreamingFileManager v-model:visible="showFileManager" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { streamingApi, StreamingWebSocket, CanvasRecorder } from '@/api/streaming'
import { Message } from '@arco-design/web-vue'
import StreamingFileManager from './StreamingFileManager.vue'

const deviceStore = useDeviceStore()

// 响应式数据
const isStreaming = ref(false)
const isRecording = ref(false)
const hasFrame = ref(false)
const connectionStatus = ref('disconnected')
const currentFps = ref(0)

const startingStream = ref(false)
const stoppingStream = ref(false)
const stoppingRecord = ref(false)
const takingScreenshot = ref(false)

const recordings = ref([])
const screenshots = ref([])
const showScreenshotViewer = ref(false)
const currentViewingScreenshot = ref('')
const showFileManager = ref(false)

const recordingFormat = ref('webm')

// 设置
const settings = reactive({
  fps: 10,
  quality: 80,
  scale: 0.5
})

// DOM引用
const streamContainer = ref(null)
const streamCanvas = ref(null)

// WebSocket和录制器
let streamingWS = null
let canvasRecorder = null
let lastFrameTime = 0
let frameCount = 0

// 计算属性
const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
})

// 方法
const initWebSocket = () => {
  streamingWS = new StreamingWebSocket()
  
  streamingWS.onStatus((status) => {
    connectionStatus.value = status
  })
  
  streamingWS.onFrame((frameData) => {
    drawFrame(frameData)
    updateFps()
  })
  
  streamingWS.onError((error) => {
    Message.error('流媒体连接错误: ' + error.message)
  })
}

const drawFrame = (frameData) => {
  if (!streamCanvas.value) return
  
  const canvas = streamCanvas.value
  const ctx = canvas.getContext('2d')
  
  // 创建图片对象
  const img = new Image()
  img.onload = () => {
    // 设置canvas尺寸
    canvas.width = img.width
    canvas.height = img.height
    
    // 绘制图片
    ctx.drawImage(img, 0, 0)
    hasFrame.value = true
  }
  
  // 设置图片数据
  img.src = `data:image/png;base64,${frameData.data}`
}

const updateFps = () => {
  const now = Date.now()
  frameCount++
  
  if (now - lastFrameTime >= 1000) {
    currentFps.value = frameCount
    frameCount = 0
    lastFrameTime = now
  }
}

const startStreaming = async () => {
  if (!deviceStore.isConnected) {
    Message.error('请先连接设备')
    return
  }
  
  startingStream.value = true
  try {
    // 连接WebSocket
    if (!streamingWS) {
      initWebSocket()
    }
    streamingWS.connect()
    
    // 启动后端流媒体
    const response = await streamingApi.startStreaming({
      device_id: deviceStore.currentDeviceId,
      fps: settings.fps,
      quality: settings.quality,
      scale: settings.scale
    })

    console.log('流媒体启动响应:', response)

    if (response.success) {
      isStreaming.value = true
      Message.success('实时流媒体已开始')
    } else {
      throw new Error(response.message || '启动失败')
    }
    
  } catch (error) {
    Message.error('启动流媒体失败: ' + error.message)
  } finally {
    startingStream.value = false
  }
}

const stopStreaming = async () => {
  stoppingStream.value = true
  try {
    // 停止录制（如果正在录制）
    if (isRecording.value) {
      await stopRecording()
    }
    
    // 停止后端流媒体
    await streamingApi.stopStreaming()
    
    // 断开WebSocket
    if (streamingWS) {
      streamingWS.disconnect()
    }
    
    isStreaming.value = false
    hasFrame.value = false
    currentFps.value = 0
    
    Message.success('流媒体已停止')
    
  } catch (error) {
    Message.error('停止流媒体失败: ' + error.message)
  } finally {
    stoppingStream.value = false
  }
}

const startRecording = async () => {
  if (recordingFormat.value === 'mp4') {
    // 服务端录制
    try {
      const response = await streamingApi.startRecording({
        device_id: deviceStore.currentDeviceId
      })

      if (response.success) {
        isRecording.value = true
        Message.success('服务端录制已开始')
      } else {
        throw new Error(response.message || '录制失败')
      }
    } catch (error) {
      Message.error('开始录制失败: ' + error.message)
    }
  } else {
    // 客户端录制
    try {
      if (!canvasRecorder) {
        canvasRecorder = new CanvasRecorder(streamCanvas.value)
      }
      
      await canvasRecorder.startRecording({
        fps: settings.fps,
        mimeType: 'video/webm;codecs=vp9'
      })
      
      isRecording.value = true
      Message.success('客户端录制已开始')
      
    } catch (error) {
      Message.error('开始录制失败: ' + error.message)
    }
  }
}

const stopRecording = async () => {
  stoppingRecord.value = true
  try {
    if (recordingFormat.value === 'mp4') {
      // 服务端录制
      const response = await streamingApi.stopRecording()

      if (response.success) {
        Message.success('服务端录制已完成')
        await refreshRecordings()
      } else {
        throw new Error(response.message || '停止录制失败')
      }
    } else {
      // 客户端录制
      if (canvasRecorder) {
        const blob = await canvasRecorder.stopRecording()
        const filename = `client_recording_${Date.now()}.webm`
        canvasRecorder.downloadRecording(blob, filename)
        Message.success('客户端录制已完成并下载')
      }
    }
    
    isRecording.value = false
    
  } catch (error) {
    Message.error('停止录制失败: ' + error.message)
  } finally {
    stoppingRecord.value = false
  }
}

const takeScreenshot = async () => {
  takingScreenshot.value = true
  try {
    const response = await streamingApi.takeScreenshot({
      device_id: deviceStore.currentDeviceId
    })

    if (response.success) {
      Message.success('截图完成')
      await refreshScreenshots()
    } else {
      throw new Error(response.message || '截图失败')
    }
    
  } catch (error) {
    Message.error('截图失败: ' + error.message)
  } finally {
    takingScreenshot.value = false
  }
}

const onCanvasClick = (event) => {
  if (!hasFrame.value) return
  
  const canvas = streamCanvas.value
  const rect = canvas.getBoundingClientRect()
  
  // 计算点击坐标
  const x = Math.round((event.clientX - rect.left) * (canvas.width / rect.width))
  const y = Math.round((event.clientY - rect.top) * (canvas.height / rect.height))
  
  console.log(`点击坐标: (${x}, ${y})`)
  Message.info(`点击坐标: (${x}, ${y})`)
}

// 文件管理方法
const refreshRecordings = async () => {
  try {
    const response = await streamingApi.getRecordings()
    recordings.value = response.data?.recordings || []
  } catch (error) {
    console.error('获取录制文件列表失败:', error)
  }
}

const refreshScreenshots = async () => {
  try {
    const response = await streamingApi.getScreenshots()
    screenshots.value = response.data?.screenshots || []
  } catch (error) {
    console.error('获取截图列表失败:', error)
  }
}

const downloadRecording = (filename) => {
  const url = streamingApi.downloadRecording(filename)
  window.open(url, '_blank')
}

const deleteRecording = async (filename) => {
  try {
    await streamingApi.deleteRecording(filename)
    Message.success('录制文件已删除')
    await refreshRecordings()
  } catch (error) {
    Message.error('删除录制文件失败: ' + error.message)
  }
}

const viewScreenshot = (filename) => {
  currentViewingScreenshot.value = filename
  showScreenshotViewer.value = true
}

const downloadScreenshot = (filename) => {
  const url = streamingApi.downloadScreenshot(filename)
  window.open(url, '_blank')
}

const deleteScreenshot = async (filename) => {
  try {
    await streamingApi.deleteScreenshot(filename)
    Message.success('截图已删除')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除截图失败: ' + error.message)
  }
}

const getScreenshotUrl = (filename) => {
  return streamingApi.downloadScreenshot(filename)
}

// 工具函数
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  return new Date(timestamp * 1000).toLocaleString()
}

// 文件管理方法
const openFileManager = () => {
  showFileManager.value = true
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    refreshRecordings(),
    refreshScreenshots()
  ])
})

onUnmounted(() => {
  if (isStreaming.value) {
    stopStreaming()
  }

  if (streamingWS) {
    streamingWS.disconnect()
  }
})
</script>

<style scoped>
.screen-streaming {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.streaming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.streaming-header h3 {
  margin: 0;
  color: var(--color-text-1);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.streaming-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--color-fill-1);
  border-radius: 6px;
  margin-bottom: 12px;
}

.control-group {
  display: flex;
  gap: 8px;
}

.status-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.fps-display {
  font-size: 12px;
  color: var(--color-text-2);
  font-family: monospace;
}

.streaming-settings {
  padding: 12px;
  background: var(--color-fill-1);
  border-radius: 6px;
  margin-bottom: 12px;
}

.streaming-display {
  flex: 1;
  min-height: 400px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 12px;
}

.stream-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.stream-canvas {
  max-width: 100%;
  max-height: 100%;
  cursor: crosshair;
  border-radius: 4px;
}

.no-stream,
.loading-stream {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--color-text-3);
}

.loading-stream p {
  margin-top: 16px;
  color: var(--color-text-2);
}

.media-management {
  height: 200px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
}

.recordings-list,
.screenshots-list {
  height: 160px;
  overflow-y: auto;
}

.recording-info,
.screenshot-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.filename {
  font-weight: 500;
  color: var(--color-text-1);
  margin-bottom: 4px;
}

.filesize,
.time {
  font-size: 12px;
  color: var(--color-text-3);
}

.recording-actions,
.screenshot-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

.screenshot-viewer {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .streaming-controls {
    flex-direction: column;
    gap: 8px;
  }

  .control-group {
    flex-wrap: wrap;
    justify-content: center;
  }

  .streaming-settings .arco-row {
    flex-direction: column;
  }

  .streaming-settings .arco-col {
    width: 100% !important;
    margin-bottom: 8px;
  }
}
</style>
