<template>
  <div
    v-if="visible"
    class="ffmpeg-stream-window"
    :style="windowStyle"
    @mousedown="startDrag"
  >
    <!-- 窗口标题栏 -->
    <div class="window-header" @mousedown="startDrag">
      <div class="window-title">
        <icon-video-camera />
        <span>FFmpeg高性能流媒体</span>
      </div>
      <div class="window-controls">
        <a-button size="mini" @click="toggleMinimize">
          <template #icon>
            <icon-minus v-if="!minimized" />
            <icon-plus v-else />
          </template>
        </a-button>
        <a-button size="mini" @click="toggleFullscreen">
          <template #icon>
            <icon-fullscreen v-if="!fullscreen" />
            <icon-fullscreen-exit v-else />
          </template>
        </a-button>
        <a-button size="mini" status="danger" @click="closeWindow">
          <template #icon><icon-close /></template>
        </a-button>
      </div>
    </div>

    <!-- 窗口内容 -->
    <div v-if="!minimized" class="window-content">
      <!-- Canvas显示器（与传统模式一致） -->
      <div class="stream-display">
        <canvas
          ref="streamCanvas"
          class="stream-canvas"
          @click="onCanvasClick"
        ></canvas>

        <div v-if="!hasFrame" class="no-stream">
          <a-spin size="large" />
          <p>正在连接FFmpeg流媒体...</p>
        </div>
      </div>

      <!-- 控制面板 -->
      <div class="stream-controls">
        <div class="control-row">
          <a-button
            v-if="!isStreaming"
            type="primary"
            @click="startStreaming"
            :loading="startingStream"
            size="mini"
          >
            <template #icon><icon-play-arrow /></template>
            开始
          </a-button>
          <a-button
            v-else
            status="danger"
            @click="stopStreaming"
            :loading="stoppingStream"
            size="mini"
          >
            <template #icon><icon-pause /></template>
            停止
          </a-button>

          <a-button
            v-if="!isRecording"
            @click="startRecording"
            :disabled="!isStreaming"
            size="mini"
          >
            <template #icon><icon-record /></template>
            录制
          </a-button>
          <a-button
            v-else
            status="warning"
            @click="stopRecording"
            :loading="stoppingRecord"
            size="mini"
          >
            <template #icon><icon-record-stop /></template>
            停止录制
          </a-button>

          <a-button
            @click="testFFmpeg"
            size="mini"
          >
            <template #icon><icon-tool /></template>
            测试FFmpeg
          </a-button>
        </div>

        <div class="status-row">
          <a-tag :color="connectionStatus === 'connected' ? 'green' : 'red'" size="small">
            {{ connectionStatusText }}
          </a-tag>
          <span v-if="streamUrls" class="stream-info">HLS流可用</span>
        </div>

        <!-- 设置面板 -->
        <div class="settings-row" v-if="!minimized">
          <a-form layout="inline" size="mini">
            <a-form-item label="帧率">
              <a-select
                v-model="streamSettings.fps"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option :value="15">15</a-option>
                <a-option :value="20">20</a-option>
                <a-option :value="30">30</a-option>
                <a-option :value="60">60</a-option>
              </a-select>
            </a-form-item>
            <a-form-item label="比特率">
              <a-select
                v-model="streamSettings.bitrate"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option value="1M">1M</a-option>
                <a-option value="2M">2M</a-option>
                <a-option value="4M">4M</a-option>
                <a-option value="8M">8M</a-option>
              </a-select>
            </a-form-item>
            <a-form-item label="分辨率">
              <a-select
                v-model="streamSettings.resolution"
                size="mini"
                style="width: 80px"
                :disabled="isStreaming"
                @change="onSettingsChange"
              >
                <a-option value="480p">480p</a-option>
                <a-option value="720p">720p</a-option>
                <a-option value="1080p">1080p</a-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>

    <!-- 调整大小手柄 -->
    <div
      v-if="!minimized && !fullscreen"
      class="resize-handle"
      @mousedown="startResize"
    ></div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import { useDeviceStore } from '@/stores/device'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'coordinate-click'])

const deviceStore = useDeviceStore()

// 窗口状态
const position = reactive({ x: 100, y: 100 })
const size = reactive({ width: 360, height: 761 }) // 与传统模式相同的默认尺寸
const minimized = ref(false)
const fullscreen = ref(false)
const isDragging = ref(false)
const isResizing = ref(false)

// 流媒体状态
const isStreaming = ref(false)
const isRecording = ref(false)
const hasFrame = ref(false)
const connectionStatus = ref('disconnected')

const startingStream = ref(false)
const stoppingStream = ref(false)
const stoppingRecord = ref(false)

// 流媒体设置
const streamSettings = reactive({
  fps: 30,
  bitrate: "2M",
  resolution: "720p"
})

// DOM引用
const streamCanvas = ref(null)

// WebSocket连接
let streamingWS = null

// 计算属性
const windowStyle = computed(() => {
  if (fullscreen.value) {
    return {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      zIndex: 9999
    }
  }
  
  return {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    width: `${size.width}px`,
    height: minimized.value ? 'auto' : `${size.height}px`,
    zIndex: 9999
  }
})

const connectionStatusText = computed(() => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
})

// 设备API调用
const deviceApi = {
  async getDeviceInfo() {
    const response = await fetch('/api/android/device/info')
    return await response.json()
  }
}

// FFmpeg API调用
const ffmpegApi = {
  async startStreaming(options) {
    const response = await fetch('/api/ffmpeg-streaming/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options)
    })
    return await response.json()
  },

  async stopStreaming() {
    const response = await fetch('/api/ffmpeg-streaming/stop', {
      method: 'POST'
    })
    return await response.json()
  },

  async startRecording(options) {
    const response = await fetch('/api/ffmpeg-streaming/recording/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options)
    })
    return await response.json()
  },

  async stopRecording() {
    const response = await fetch('/api/ffmpeg-streaming/recording/stop', {
      method: 'POST'
    })
    return await response.json()
  },

  async getStatus() {
    const response = await fetch('/api/ffmpeg-streaming/status')
    return await response.json()
  },

  async testFFmpeg() {
    const response = await fetch('/api/ffmpeg-streaming/test')
    return await response.json()
  }
}

// 窗口尺寸设置
const setWindowSizeFromDevice = async () => {
  try {
    const response = await deviceApi.getDeviceInfo()
    console.log('设备信息响应:', response)

    if (response.data && response.data.screen_info) {
      const screenInfo = response.data.screen_info
      let deviceWidth = screenInfo.width || 1080
      let deviceHeight = screenInfo.height || 1920

      // 如果设备是横屏，交换宽高，确保竖屏显示
      if (deviceWidth > deviceHeight) {
        [deviceWidth, deviceHeight] = [deviceHeight, deviceWidth]
      }

      // 计算设备的宽高比
      const deviceAspectRatio = deviceWidth / deviceHeight

      // 设置显示区域的最大尺寸
      const maxDisplayWidth = Math.min(450, window.innerWidth * 0.35)
      const maxDisplayHeight = Math.min(700, window.innerHeight * 0.65)

      // 根据设备宽高比计算显示区域尺寸
      let displayWidth, displayHeight

      if (maxDisplayWidth / maxDisplayHeight > deviceAspectRatio) {
        displayHeight = maxDisplayHeight
        displayWidth = Math.round(displayHeight * deviceAspectRatio)
      } else {
        displayWidth = maxDisplayWidth
        displayHeight = Math.round(displayWidth / deviceAspectRatio)
      }

      // 计算窗口总尺寸
      const headerHeight = 41   // 标题栏高度
      const controlsHeight = 80 // 控制面板高度

      size.width = displayWidth
      size.height = displayHeight + headerHeight + controlsHeight

      console.log(`设备屏幕: ${deviceWidth}x${deviceHeight} (比例: ${deviceAspectRatio.toFixed(2)})`)
      console.log(`显示区域: ${displayWidth}x${displayHeight}`)
      console.log(`窗口总尺寸: ${size.width}x${size.height}`)
    }
  } catch (error) {
    console.error('获取设备信息失败:', error)
    // 使用默认尺寸（9:16比例）
    const defaultDisplayWidth = 360
    const defaultDisplayHeight = 640
    size.width = defaultDisplayWidth
    size.height = defaultDisplayHeight + 121
  }
}

// 流媒体功能
const startStreaming = async () => {
  if (!deviceStore.isConnected) {
    Message.error('请先连接设备')
    return
  }

  startingStream.value = true
  try {
    // 首先设置窗口尺寸
    await setWindowSizeFromDevice()

    // 使用传统流媒体API，但使用FFmpeg优化的参数
    const response = await fetch('/api/streaming/start', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        device_id: deviceStore.currentDeviceId,
        fps: streamSettings.fps,
        quality: 80, // 更高质量
        scale: 0.8,  // 更好的缩放
        mode: 'ffmpeg' // 标记为FFmpeg模式
      })
    })

    const result = await response.json()

    if (result.success) {
      isStreaming.value = true
      connectionStatus.value = 'connecting'

      // 启动WebSocket连接
      initWebSocket()
      connectWebSocket()

      Message.success('FFmpeg优化流媒体已开始')
    } else {
      throw new Error(result.message || '启动失败')
    }

  } catch (error) {
    console.error('启动FFmpeg流媒体失败:', error)
    Message.error('启动FFmpeg流媒体失败: ' + error.message)
  } finally {
    startingStream.value = false
  }
}

const stopStreaming = async () => {
  stoppingStream.value = true
  try {
    // 使用传统流媒体停止API
    await fetch('/api/streaming/stop', { method: 'POST' })

    isStreaming.value = false
    hasFrame.value = false
    connectionStatus.value = 'disconnected'

    // 断开WebSocket连接
    if (streamingWS) {
      streamingWS.close()
      streamingWS = null
    }

    Message.success('FFmpeg流媒体已停止')

  } catch (error) {
    Message.error('停止FFmpeg流媒体失败: ' + error.message)
  } finally {
    stoppingStream.value = false
  }
}

// WebSocket相关方法
const initWebSocket = () => {
  // 使用传统流媒体的WebSocket端点，但启动FFmpeg优化的后端
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const host = window.location.hostname
  const port = process.env.NODE_ENV === 'development' ? '8000' : window.location.port
  const wsUrl = `${protocol}//${host}:${port}/api/streaming/stream`

  console.log('FFmpeg WebSocket连接地址:', wsUrl)
  streamingWS = new WebSocket(wsUrl)

  streamingWS.onopen = () => {
    console.log('FFmpeg WebSocket连接已建立')
    connectionStatus.value = 'connected'
  }

  streamingWS.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data)

      if (data.type === 'frame') {
        drawFrame(data)
      } else if (data.type === 'connection') {
        console.log('FFmpeg WebSocket连接确认:', data.message)
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  streamingWS.onclose = () => {
    console.log('FFmpeg WebSocket连接已关闭')
    connectionStatus.value = 'disconnected'
    hasFrame.value = false
  }

  streamingWS.onerror = (error) => {
    console.error('FFmpeg WebSocket连接错误:', error)
    connectionStatus.value = 'disconnected'
  }
}

const connectWebSocket = () => {
  if (streamingWS && streamingWS.readyState === WebSocket.OPEN) {
    return
  }

  if (streamingWS) {
    streamingWS.close()
  }

  initWebSocket()
}

const drawFrame = (frameData) => {
  if (!streamCanvas.value) return

  const canvas = streamCanvas.value
  const ctx = canvas.getContext('2d')

  const img = new Image()
  img.onload = () => {
    // 获取容器的实际尺寸
    const container = canvas.parentElement
    const containerWidth = container.clientWidth
    const containerHeight = container.clientHeight

    // 设置Canvas的显示尺寸为容器尺寸
    canvas.style.width = containerWidth + 'px'
    canvas.style.height = containerHeight + 'px'

    // 设置Canvas的内部分辨率
    const pixelRatio = window.devicePixelRatio || 1
    canvas.width = containerWidth * pixelRatio
    canvas.height = containerHeight * pixelRatio

    // 缩放绘图上下文
    ctx.scale(pixelRatio, pixelRatio)

    // 清空画布并绘制图片
    ctx.clearRect(0, 0, containerWidth, containerHeight)
    ctx.drawImage(img, 0, 0, containerWidth, containerHeight)
    hasFrame.value = true
  }

  img.src = `data:image/${frameData.format};base64,${frameData.data}`
}

const startRecording = async () => {
  try {
    const response = await ffmpegApi.startRecording({
      device_id: deviceStore.currentDeviceId
    })

    if (response.success) {
      isRecording.value = true
      Message.success('FFmpeg录制已开始')
    } else {
      throw new Error(response.message || '录制失败')
    }
  } catch (error) {
    Message.error('开始FFmpeg录制失败: ' + error.message)
  }
}

const stopRecording = async () => {
  stoppingRecord.value = true
  try {
    const response = await ffmpegApi.stopRecording()

    if (response.success) {
      Message.success('FFmpeg录制已完成')
    } else {
      throw new Error(response.message || '停止录制失败')
    }

    isRecording.value = false

  } catch (error) {
    Message.error('停止FFmpeg录制失败: ' + error.message)
  } finally {
    stoppingRecord.value = false
  }
}

const testFFmpeg = async () => {
  try {
    const response = await ffmpegApi.testFFmpeg()

    if (response.success) {
      Message.success(`FFmpeg可用: ${response.version}`)
    } else {
      Message.error(`FFmpeg不可用: ${response.message}`)
    }
  } catch (error) {
    Message.error('测试FFmpeg失败: ' + error.message)
  }
}

// Canvas事件处理
const onCanvasClick = (event) => {
  if (!hasFrame.value) return

  const canvas = streamCanvas.value
  const rect = canvas.getBoundingClientRect()

  // 计算在canvas显示区域上的相对坐标
  const canvasX = event.clientX - rect.left
  const canvasY = event.clientY - rect.top

  // 计算相对位置（0-1之间）
  const relativeX = canvasX / rect.width
  const relativeY = canvasY / rect.height

  // 转换为设备上的实际坐标
  const deviceWidth = 1080  // 临时值，应该从设备信息获取
  const deviceHeight = 1920 // 临时值，应该从设备信息获取

  const x = Math.round(relativeX * deviceWidth)
  const y = Math.round(relativeY * deviceHeight)

  emit('coordinate-click', { x, y })
  Message.info(`点击坐标: (${x}, ${y})`)
}

// 窗口控制功能
const startDrag = (event) => {
  if (fullscreen.value) return

  isDragging.value = true
  const startX = event.clientX - position.x
  const startY = event.clientY - position.y

  const onMouseMove = (e) => {
    if (!isDragging.value) return
    position.x = Math.max(0, Math.min(window.innerWidth - size.width, e.clientX - startX))
    position.y = Math.max(0, Math.min(window.innerHeight - 100, e.clientY - startY))
  }

  const onMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
  event.preventDefault()
}

const startResize = (event) => {
  isResizing.value = true
  const startX = event.clientX
  const startY = event.clientY
  const startWidth = size.width
  const startHeight = size.height

  const onMouseMove = (e) => {
    if (!isResizing.value) return
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    size.width = Math.max(400, startWidth + deltaX)
    size.height = Math.max(300, startHeight + deltaY)
  }

  const onMouseUp = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }

  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
  event.preventDefault()
  event.stopPropagation()
}

const toggleMinimize = () => {
  minimized.value = !minimized.value
}

const toggleFullscreen = () => {
  fullscreen.value = !fullscreen.value
}

const closeWindow = () => {
  if (isStreaming.value) {
    stopStreaming()
  }
  emit('close')
}

const onSettingsChange = () => {
  if (isStreaming.value) {
    Message.info('设置将在下次启动流媒体时生效')
  }
}

// 监听可见性变化
watch(() => props.visible, async (visible) => {
  if (visible) {
    console.log('FFmpeg窗口显示，设置设备尺寸')
    await setWindowSizeFromDevice()

    // 如果设备已连接且没有在流媒体，自动启动
    if (deviceStore.isConnected && !isStreaming.value && !startingStream.value) {
      console.log('自动启动FFmpeg流媒体')
      setTimeout(() => {
        startStreaming()
      }, 300)
    }
  } else {
    // 窗口隐藏时停止流媒体
    if (isStreaming.value) {
      console.log('FFmpeg窗口隐藏，停止流媒体')
      stopStreaming()
    }
  }
})

// 生命周期
onMounted(async () => {
  console.log('FFmpeg流媒体组件已挂载')
  // 组件挂载时立即设置窗口尺寸
  if (deviceStore.isConnected) {
    console.log('组件挂载，设置设备尺寸')
    await setWindowSizeFromDevice()
  }
})

onUnmounted(() => {
  if (isStreaming.value) {
    stopStreaming()
  }

  if (streamingWS) {
    streamingWS.close()
  }
})
</script>

<style scoped>
.ffmpeg-stream-window {
  background: var(--color-bg-2);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  user-select: none;
  min-width: 400px;
  min-height: 300px;
}

.window-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--color-fill-2);
  border-bottom: 1px solid var(--color-border);
  cursor: move;
}

.window-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-text-1);
}

.window-controls {
  display: flex;
  gap: 4px;
}

.window-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 41px);
}

.stream-display {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.stream-canvas {
  width: 100%;
  height: 100%;
  cursor: crosshair;
  display: block;
}

.no-stream {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
}

.no-stream p {
  margin-top: 12px;
  font-size: 12px;
}

.stream-controls {
  padding: 8px;
  background: var(--color-fill-1);
  border-top: 1px solid var(--color-border);
}

.control-row {
  display: flex;
  gap: 4px;
  margin-bottom: 6px;
  flex-wrap: wrap;
}

.status-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.stream-info {
  color: var(--color-text-3);
  font-family: monospace;
}

.settings-row {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid var(--color-border-2);
}

.settings-row .arco-form {
  margin: 0;
}

.settings-row .arco-form-item {
  margin-bottom: 0;
}

.settings-row .arco-form-item-label {
  font-size: 11px;
  color: var(--color-text-3);
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: se-resize;
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-border) 30%,
    var(--color-border) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-border) 60%,
    var(--color-border) 70%,
    transparent 70%
  );
}

.resize-handle:hover {
  background: linear-gradient(
    -45deg,
    transparent 30%,
    var(--color-primary) 30%,
    var(--color-primary) 40%,
    transparent 40%,
    transparent 60%,
    var(--color-primary) 60%,
    var(--color-primary) 70%,
    transparent 70%
  );
}
</style>
