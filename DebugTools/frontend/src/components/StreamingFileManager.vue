<template>
  <a-modal
    :visible="visible"
    title="流媒体文件管理"
    width="80%"
    :footer="false"
    :z-index="floatingMode ? 10000 : 1000"
    @cancel="$emit('update:visible', false)"
    @ok="$emit('update:visible', false)"
  >
    <div class="streaming-file-manager">
      <a-tabs default-active-key="recordings" size="small">
        <!-- 录制文件 -->
        <a-tab-pane key="recordings" title="录制文件">
          <div class="file-section">
            <div class="section-header">
              <h4>录制文件列表</h4>
              <div class="header-actions">
                <a-button @click="refreshRecordings" :loading="loadingRecordings" size="small">
                  <template #icon><icon-refresh /></template>
                  刷新
                </a-button>
                <a-button @click="openRecordingsFolder" size="small">
                  <template #icon><icon-folder /></template>
                  打开文件夹
                </a-button>
              </div>
            </div>

            <a-table
              :data="recordings"
              :loading="loadingRecordings"
              :pagination="{ pageSize: 10 }"
              size="small"
            >
              <template #columns>
                <a-table-column title="文件名" data-index="filename" :width="300">
                  <template #cell="{ record }">
                    <div class="file-info">
                      <icon-video />
                      <span class="filename">{{ record.filename }}</span>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="文件大小" data-index="size" :width="120">
                  <template #cell="{ record }">
                    {{ formatFileSize(record.size) }}
                  </template>
                </a-table-column>
                <a-table-column title="创建时间" data-index="created_time" :width="180">
                  <template #cell="{ record }">
                    {{ formatTime(record.created_time) }}
                  </template>
                </a-table-column>
                <a-table-column title="操作" :width="200">
                  <template #cell="{ record }">
                    <a-space size="small">
                      <a-button @click="downloadRecording(record.filename)" size="mini">
                        <template #icon><icon-download /></template>
                        下载
                      </a-button>
                      <a-button @click="previewRecording(record)" size="mini">
                        <template #icon><icon-eye /></template>
                        预览
                      </a-button>
                      <a-popconfirm
                        content="确定要删除这个录制文件吗？"
                        @ok="deleteRecording(record.filename)"
                      >
                        <a-button size="mini" status="danger">
                          <template #icon><icon-delete /></template>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </a-table-column>
              </template>
              <template #empty>
                <a-empty description="暂无录制文件" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 截图文件 -->
        <a-tab-pane key="screenshots" title="截图文件">
          <div class="file-section">
            <div class="section-header">
              <h4>截图文件列表</h4>
              <div class="header-actions">
                <a-button @click="refreshScreenshots" :loading="loadingScreenshots" size="small">
                  <template #icon><icon-refresh /></template>
                  刷新
                </a-button>
                <a-button @click="openScreenshotsFolder" size="small">
                  <template #icon><icon-folder /></template>
                  打开文件夹
                </a-button>
              </div>
            </div>

            <a-table
              :data="screenshots"
              :loading="loadingScreenshots"
              :pagination="{ pageSize: 10 }"
              size="small"
            >
              <template #columns>
                <a-table-column title="文件名" data-index="filename" :width="300">
                  <template #cell="{ record }">
                    <div class="file-info">
                      <icon-image />
                      <span class="filename">{{ record.filename }}</span>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column title="文件大小" data-index="size" :width="120">
                  <template #cell="{ record }">
                    {{ formatFileSize(record.size) }}
                  </template>
                </a-table-column>
                <a-table-column title="创建时间" data-index="created_time" :width="180">
                  <template #cell="{ record }">
                    {{ formatTime(record.created_time) }}
                  </template>
                </a-table-column>
                <a-table-column title="操作" :width="200">
                  <template #cell="{ record }">
                    <a-space size="small">
                      <a-button @click="downloadScreenshot(record.filename)" size="mini">
                        <template #icon><icon-download /></template>
                        下载
                      </a-button>
                      <a-button @click="previewScreenshot(record)" size="mini">
                        <template #icon><icon-eye /></template>
                        预览
                      </a-button>
                      <a-popconfirm
                        content="确定要删除这个截图吗？"
                        @ok="deleteScreenshot(record.filename)"
                      >
                        <a-button size="mini" status="danger">
                          <template #icon><icon-delete /></template>
                          删除
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </a-table-column>
              </template>
              <template #empty>
                <a-empty description="暂无截图文件" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 预览模态框 -->
    <a-modal
      :visible="showPreview"
      :title="previewFile?.filename"
      width="70%"
      :footer="false"
      @cancel="showPreview = false"
      @ok="showPreview = false"
    >
      <div class="preview-content">
        <img
          v-if="previewFile?.type === 'image'"
          :src="previewFile.url"
          :alt="previewFile.filename"
          style="max-width: 100%; height: auto;"
        />
        <video
          v-else-if="previewFile?.type === 'video'"
          :src="previewFile.url"
          controls
          style="max-width: 100%; height: auto;"
        >
          您的浏览器不支持视频播放。
        </video>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { streamingApi } from '@/api/streaming'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  floatingMode: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 响应式数据
const recordings = ref([])
const screenshots = ref([])
const loadingRecordings = ref(false)
const loadingScreenshots = ref(false)
const showPreview = ref(false)
const previewFile = ref(null)

// 方法
const refreshRecordings = async () => {
  loadingRecordings.value = true
  try {
    const response = await streamingApi.getRecordings()
    recordings.value = response.data?.recordings || []
  } catch (error) {
    Message.error('获取录制文件列表失败: ' + error.message)
  } finally {
    loadingRecordings.value = false
  }
}

const refreshScreenshots = async () => {
  loadingScreenshots.value = true
  try {
    const response = await streamingApi.getScreenshots()
    screenshots.value = response.data?.screenshots || []
  } catch (error) {
    Message.error('获取截图列表失败: ' + error.message)
  } finally {
    loadingScreenshots.value = false
  }
}

const downloadRecording = (filename) => {
  const url = streamingApi.downloadRecording(filename)
  window.open(url, '_blank')
}

const downloadScreenshot = (filename) => {
  const url = streamingApi.downloadScreenshot(filename)
  window.open(url, '_blank')
}

const previewRecording = (record) => {
  previewFile.value = {
    filename: record.filename,
    type: 'video',
    url: streamingApi.downloadRecording(record.filename)
  }
  showPreview.value = true
}

const previewScreenshot = (record) => {
  previewFile.value = {
    filename: record.filename,
    type: 'image',
    url: streamingApi.downloadScreenshot(record.filename)
  }
  showPreview.value = true
}

const deleteRecording = async (filename) => {
  try {
    await streamingApi.deleteRecording(filename)
    Message.success('录制文件已删除')
    await refreshRecordings()
  } catch (error) {
    Message.error('删除录制文件失败: ' + error.message)
  }
}

const deleteScreenshot = async (filename) => {
  try {
    await streamingApi.deleteScreenshot(filename)
    Message.success('截图已删除')
    await refreshScreenshots()
  } catch (error) {
    Message.error('删除截图失败: ' + error.message)
  }
}

const openRecordingsFolder = () => {
  // 调用后端API打开文件夹
  fetch('/api/streaming/recordings/open-folder', { method: 'POST' })
    .then(() => Message.success('已尝试打开录制文件夹'))
    .catch(() => Message.error('打开文件夹失败'))
}

const openScreenshotsFolder = () => {
  // 调用后端API打开文件夹
  fetch('/api/streaming/screenshots/open-folder', { method: 'POST' })
    .then(() => Message.success('已尝试打开截图文件夹'))
    .catch(() => Message.error('打开文件夹失败'))
}

// 工具函数
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  return new Date(timestamp * 1000).toLocaleString()
}

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible) {
    refreshRecordings()
    refreshScreenshots()
  }
})
</script>

<style scoped>
.streaming-file-manager {
  min-height: 500px;
}

.file-section {
  padding: 16px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filename {
  font-weight: 500;
}

.preview-content {
  text-align: center;
  max-height: 70vh;
  overflow: auto;
}
</style>
