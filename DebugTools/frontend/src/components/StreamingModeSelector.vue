<template>
  <div class="streaming-mode-selector">
    <div class="mode-selector-compact">
      <div class="mode-header">
        <h4>流媒体方式</h4>
        <a-button @click="testFFmpeg" :loading="testingFFmpeg" size="mini">
          <template #icon><icon-tool /></template>
          测试FFmpeg
        </a-button>
      </div>

      <div class="mode-options">
        <a-radio-group v-model="selectedMode" @change="onModeChange" size="small">
          <a-space>
            <!-- 传统方式 -->
            <a-radio value="traditional">
              <div class="mode-option-compact">
                <icon-image class="mode-icon" />
                <span class="mode-title">传统模式</span>
                <a-tag color="orange" size="mini">稳定</a-tag>
              </div>
            </a-radio>

            <!-- FFmpeg方式 -->
            <a-radio value="ffmpeg" :disabled="!ffmpegAvailable">
              <div class="mode-option-compact">
                <icon-video-camera class="mode-icon" />
                <span class="mode-title">FFmpeg模式</span>
                <a-tag color="green" size="mini">推荐</a-tag>
                <a-tag v-if="!ffmpegAvailable" color="red" size="mini">不可用</a-tag>
              </div>
            </a-radio>
          </a-space>
        </a-radio-group>
      </div>

      <div v-if="ffmpegInfo" class="ffmpeg-status">
        <icon-check-circle style="color: green; font-size: 12px;" />
        <span>FFmpeg可用</span>
      </div>

      <!-- FFmpeg安装提示（简化版） -->
      <div v-if="!ffmpegAvailable" class="ffmpeg-warning">
        <a-alert
          type="warning"
          message="FFmpeg不可用，请安装后重新测试"
          size="small"
          show-icon
          closable
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'

const emit = defineEmits(['mode-change'])

// 状态
const selectedMode = ref('traditional')
const ffmpegAvailable = ref(false)
const ffmpegInfo = ref('')
const testingFFmpeg = ref(false)

// FFmpeg API
const ffmpegApi = {
  async test() {
    const response = await fetch('/api/ffmpeg-streaming/test')
    return await response.json()
  }
}

// 测试FFmpeg
const testFFmpeg = async () => {
  testingFFmpeg.value = true
  try {
    const response = await ffmpegApi.test()
    
    if (response.success) {
      ffmpegAvailable.value = true
      ffmpegInfo.value = response.version
      Message.success('FFmpeg可用: ' + response.version)
      
      // 如果FFmpeg可用且当前选择的是传统方式，建议切换
      if (selectedMode.value === 'traditional') {
        Message.info('建议使用FFmpeg方式获得更好的性能')
      }
    } else {
      ffmpegAvailable.value = false
      ffmpegInfo.value = ''
      Message.error('FFmpeg不可用: ' + response.message)
      
      // 如果当前选择FFmpeg但不可用，切换到传统方式
      if (selectedMode.value === 'ffmpeg') {
        selectedMode.value = 'traditional'
        Message.warning('已切换到传统方式')
      }
    }
  } catch (error) {
    ffmpegAvailable.value = false
    ffmpegInfo.value = ''
    Message.error('测试FFmpeg失败: ' + error.message)
  } finally {
    testingFFmpeg.value = false
  }
}

// 模式变化处理
const onModeChange = (value) => {
  console.log('流媒体模式变更:', value)
  
  if (value === 'ffmpeg' && !ffmpegAvailable.value) {
    Message.warning('FFmpeg不可用，请先安装并测试')
    return
  }
  
  emit('mode-change', value)
}

// 应用设置
const applyMode = () => {
  if (!selectedMode.value) {
    Message.warning('请选择一种流媒体方式')
    return
  }
  
  if (selectedMode.value === 'ffmpeg' && !ffmpegAvailable.value) {
    Message.error('FFmpeg不可用，无法应用此设置')
    return
  }
  
  // 保存到本地存储
  localStorage.setItem('streaming_mode', selectedMode.value)
  
  emit('mode-change', selectedMode.value)
  Message.success(`已应用${selectedMode.value === 'ffmpeg' ? 'FFmpeg' : '传统'}流媒体方式`)
}

// 组件挂载时初始化
onMounted(async () => {
  // 从本地存储读取设置
  const savedMode = localStorage.getItem('streaming_mode')
  if (savedMode) {
    selectedMode.value = savedMode
  }
  
  // 自动测试FFmpeg
  await testFFmpeg()
  
  // 如果FFmpeg可用且没有保存的设置，推荐使用FFmpeg
  if (ffmpegAvailable.value && !savedMode) {
    selectedMode.value = 'ffmpeg'
    Message.info('检测到FFmpeg可用，已自动选择高性能模式')
  }
  
  // 发送初始模式
  emit('mode-change', selectedMode.value)
})
</script>

<style scoped>
.streaming-mode-selector {
  width: 100%;
}

.mode-selector-compact {
  padding: 12px;
  background: var(--color-fill-1);
  border-radius: 6px;
  border: 1px solid var(--color-border-2);
}

.mode-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.mode-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-1);
}

.mode-options {
  margin-bottom: 8px;
}

.mode-option-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 0;
}

.mode-icon {
  font-size: 14px;
  color: var(--color-primary);
}

.mode-title {
  font-size: 12px;
  font-weight: 500;
}

.ffmpeg-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--color-success);
  margin-top: 8px;
}

.ffmpeg-warning {
  margin-top: 8px;
}

.ffmpeg-warning .arco-alert {
  padding: 6px 8px;
}
</style>
