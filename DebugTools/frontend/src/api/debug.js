import request from '@/utils/request'

export const debugApi = {
  // 执行ADB命令
  executeAdbCommand(data) {
    return request.post('/api/android/debug/adb/execute', data)
  },
  
  // 执行Poco查询
  executePocoQuery(data) {
    return request.post('/api/android/debug/poco/execute', data)
  },
  
  // 获取ADB历史记录
  getAdbHistory() {
    return request.get('/api/android/debug/adb/history')
  },
  
  // 获取Poco历史记录
  getPocoHistory() {
    return request.get('/api/android/debug/poco/history')
  },
  
  // 获取所有历史记录
  getAllHistory() {
    return request.get('/api/android/debug/history')
  },
  
  // 清空历史记录
  clearHistory() {
    return request.delete('/api/android/debug/history/clear')
  },
  
  // 获取设备日志
  getDeviceLogs() {
    return request.get('/api/android/debug/logcat')
  },

  // 获取实时设备日志
  getRealtimeDeviceLogs() {
    return request.get('/api/android/debug/logcat/realtime')
  },

  // 清空设备日志
  clearDeviceLogs() {
    return request.get('/api/android/debug/logcat/clear')
  }
}
