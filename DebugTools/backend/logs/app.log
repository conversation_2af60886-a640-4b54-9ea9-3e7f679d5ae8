2025-06-11 14:08:10 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:08:12 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:08:13 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:08:20 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ehnlwc06h_1749622074158 断开连接
2025-06-11 14:08:35 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:08:36 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:08:42 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ehnlwc06h_1749622074158 断开连接
2025-06-11 14:08:44 | ERROR    | api.operation_router:click_element:204 - 点击操作失败: 
2025-06-11 14:09:35 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ehnlwc06h_1749622074158 断开连接
2025-06-11 14:09:39 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:09:42 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:09:44 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:09:45 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:10:14 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (863, 1495) 成功
2025-06-11 14:10:52 | INFO     | services.airtest_poco_service:take_screenshot:182 - 截图保存至: screenshots/screenshot_20250611_141051.png
2025-06-11 14:10:59 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (890, 2187) 成功
2025-06-11 14:11:20 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k3k4gmphl_1749622175222 断开连接
2025-06-11 14:11:46 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 
2025-06-11 14:11:46 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('成人')
2025-06-11 14:11:48 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k3k4gmphl_1749622175222 断开连接
2025-06-11 14:11:51 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:12:00 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k3k4gmphl_1749622175222 断开连接
2025-06-11 14:12:01 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:12:03 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:12:05 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:12:06 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:12:13 | ERROR    | services.airtest_poco_service:execute_poco_query:660 - 执行Poco语句 'poco('成人')' 失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-11 14:12:13 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'AirtestPocoService' object has no attribute 'pocopoco'
2025-06-11 14:12:13 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('成人')
2025-06-11 14:12:27 | INFO     | services.airtest_poco_service:execute_poco_query:657 - 执行Poco语句 '('成人')' 成功
2025-06-11 14:12:27 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: ('成人')
2025-06-11 14:12:39 | ERROR    | services.airtest_poco_service:execute_poco_query:660 - 执行Poco语句 '('成人').text' 失败: 'UIObjectProxy' object has no attribute 'text'
2025-06-11 14:12:39 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: 'UIObjectProxy' object has no attribute 'text'
2025-06-11 14:12:39 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: ('成人').text
2025-06-11 14:12:53 | ERROR    | services.airtest_poco_service:execute_poco_query:660 - 执行Poco语句 '('成人').click()' 失败: Cannot find any visible node by query UIObjectProxy of "成人"
2025-06-11 14:12:53 | ERROR    | api.debug_router:execute_poco_query:90 - 执行Poco查询失败: Cannot find any visible node by query UIObjectProxy of "成人"
2025-06-11 14:12:53 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: ('成人').click()
2025-06-11 14:14:29 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_zmb7ytmha_1749622320439 断开连接
2025-06-11 14:15:09 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_v4sisc1iw_1749622469877 断开连接
2025-06-11 14:15:31 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_btp2o5qob_1749622510160 断开连接
2025-06-11 14:15:38 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:16:14 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:16:17 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:16:17 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:16:25 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:16:26 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:16:28 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_btp2o5qob_1749622510160 断开连接
2025-06-11 14:17:14 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:17:14 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:17:14 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:17:14 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:17:16 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:17:27 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:17:29 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:17:30 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:17:36 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_z8hew3fu0_1749622629872 断开连接
2025-06-11 14:17:58 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:18:28 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:18:28 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:18:30 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:18:30 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:18:33 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:18:33 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:19:12 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k2pc0gip8_1749622709979 断开连接
2025-06-11 14:19:19 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:19:22 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k2pc0gip8_1749622709979 断开连接
2025-06-11 14:19:40 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k2pc0gip8_1749622709979 断开连接
2025-06-11 14:20:01 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_k2pc0gip8_1749622709979 断开连接
2025-06-11 14:20:10 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ucul6hqf0_1749622802038 断开连接
2025-06-11 14:20:29 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ucul6hqf0_1749622802038 断开连接
2025-06-11 14:20:31 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:20:45 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:20:48 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:20:48 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:22:06 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:23:11 | INFO     | api.operation_router:delete_all_screenshots:158 - 删除了 6 个截图文件
2025-06-11 14:24:19 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_ucul6hqf0_1749622802038 断开连接
2025-06-11 14:25:31 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:25:50 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:26:26 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:26:34 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:26:35 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:26:38 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:26:39 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:27:11 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (884, 1567) 成功
2025-06-11 14:27:18 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (60, 350) 成功
2025-06-11 14:27:22 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (869, 1567) 成功
2025-06-11 14:27:27 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (72, 353) 成功
2025-06-11 14:27:34 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (69, 356) 成功
2025-06-11 14:27:38 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (75, 131) 成功
2025-06-11 14:27:40 | INFO     | services.airtest_poco_service:click_by_coordinates:443 - 点击坐标 (75, 131) 成功
2025-06-11 14:28:22 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
2025-06-11 14:30:17 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_9leqa6n2w_1749623416842 断开连接
2025-06-11 14:32:13 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:32:29 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:33:00 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:33:14 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:33:36 | ERROR    | api.device_router:get_device_info:113 - 获取设备信息失败: 
2025-06-11 14:35:10 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_6wu5z099n_1749623702444 断开连接
2025-06-11 14:35:17 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:35:19 | INFO     | services.device_service:scan_devices:50 - 扫描到 1 个设备: ['3ef2ce8b']
2025-06-11 14:35:23 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_e2gd1m97n_1749623713919 断开连接
2025-06-11 14:35:26 | INFO     | services.airtest_poco_service:connect_device:36 - 正在连接设备: 3ef2ce8b
2025-06-11 14:35:29 | INFO     | services.airtest_poco_service:connect_device:54 - 设备 3ef2ce8b 连接成功
2025-06-11 14:35:30 | INFO     | services.device_service:connect_device:84 - 设备 3ef2ce8b 连接成功
2025-06-11 14:35:49 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:35:49 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 120233
2025-06-11 14:35:59 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:35:59 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 67632
2025-06-11 14:36:04 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:36:04 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 61454
2025-06-11 14:36:41 | INFO     | services.airtest_poco_service:execute_poco_query:691 - 执行Poco语句: self.poco('ssss')
2025-06-11 14:36:42 | INFO     | services.airtest_poco_service:execute_poco_query:699 - Poco语句执行成功: 元素不存在
2025-06-11 14:36:42 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('ssss')
2025-06-11 14:37:17 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:37:17 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 64872
2025-06-11 14:37:31 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -c
2025-06-11 14:37:31 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 12
2025-06-11 14:37:32 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:37:32 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 137
2025-06-11 14:37:48 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:37:48 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 79985
2025-06-11 14:38:00 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -c
2025-06-11 14:38:00 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 12
2025-06-11 14:38:21 | INFO     | services.airtest_poco_service:execute_poco_query:691 - 执行Poco语句: self.poco('1111')
2025-06-11 14:38:22 | INFO     | services.airtest_poco_service:execute_poco_query:699 - Poco语句执行成功: 元素不存在
2025-06-11 14:38:22 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco('1111')
2025-06-11 14:39:00 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 14:39:01 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 112
2025-06-11 14:39:01 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: am start -n ctrip.english.debug/com.ctrip.ibu.myctrip.main.module.home.IBUHomeActivity --es hideDebug 1 --es configEnv eyJsYW5ndWFnZUNvZGUiOiAiemgiLCAidGhlbWUiOiAiSUJVVGhlbWVNb2RlU3lzdGVtIiwgImxvY2FsZUNvZGUiOiAiIiwgImN1cnJlbmN5IjogIkhLRCIsICJhYlRlc3RzIjogW10sICJtb2JpbGVDb25maWdTd2l0Y2hMaXN0IjogW10sICJob3RlbE1vY2tLZXkiOiAiIiwgInRhcmdldFVSSSI6ICIvcm5feHRhcm9faG90ZWxfY29tbWVudF9saXN0L19jcm5fY29uZmlnP0NSTlR5cGU9MSZob3RlbC1pZD0yNDAwNjcwNSZzb2xkLW91dD1GJm9uZS13b3JkLXJldmlldy1pZD05NTExNjk1NjUmaXNGYW1pbHlTY2VuZT0wJnRhZy1pZD1udWxsJnJldXNlSW5zdGFuY2U9MSZDUk5Nb2R1bGVOYW1lPXh0YXJvQ29tbWVudExpc3QmaW5pdGlhbFBhZ2U9eHRhcm9Db21tZW50TGlzdCZjaXR5TmFtZT01cUtGNXBhdjU0bTUgJnRhc2tJZD0xNzIyNzQmY2FzZUlkPTAmY2FueW9uUmVwb3J0SUQ9MTcyMjc0X21wYWFzX2F1dG90ZXN0IiwgIm1haW5OZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAibWFpbk5ldHdvcmtTdWJFbnYiOiAiZmF0NTYyNyIsICJpYnVOZXR3b3JrTWFpbkVudiI6ICJGQVQiLCAiaWJ1TmV0d29ya1N1YkVudiI6ICJmYXQ1NjI3IiwgImZsaWdodE1vY2tLZXkiOiAiNTUzNjQ2MzEiLCAiZW5hYmxlU09UUE92ZXJIdHRwIjogImZhbHNlIiwgInJlcGxhY2VNb2JpbGVDb25maWciOiAiIiwgImlzRm9yY2VEaXNhYmxlT25lVGltZVBvcHVwV2luZG93IjogInRydWUiLCAibGlzdGVuUG9ydCI6IDAsICJ1c2VybmFtZSI6ICIiLCAicGFzc3dvcmQiOiAiIiwgIlVybEV4dCI6IHsiY2FueW9uUmVwb3J0SUQiOiAiMTcyMjc0X21wYWFzX2F1dG90ZXN0In0sICJTZXRDb29yZGluYXRlIjogeyJsb25naXR1ZGUiOiAxMjEuNDczNywgImxhdGl0dWRlIjogMzEuMjMwNH0sICJsb2NrQ1JOQnVpbGRJRCI6IFt7InByb2R1Y3ROYW1lIjogInJuX3h0YXJvX2hvdGVsX2NvbW1lbnRfbGlzdCIsICJidWlsZElEIjogIjI4MjczOTIzIiwgImxvY2siOiB0cnVlfV0sICJMb2dpbkluZm8iOiB7InJldHVybkNvZGUiOiAwLCAibWVzc2FnZSI6ICJcdTYyMTBcdTUyOWYiLCAidWlkIjogIl9USUhLMTA1cjgwcjV5NWJkIiwgInBhc3N3b3JkIjogIjEyMzQ1NmFzZCIsICJkdWlkIjogInU9MEZCMDJBNzJFNkI1M0E2RkUxQTY2QkI2MzQzOUI1QzJEODE5M0NGMTFBOTE0RkVDMTAzOUVBNzlBNDk4MUNEMyZ2PTAiLCAidGlja2V0IjogIkQ2MDE0OENBMENBMzMwNDIzRUQxQTU1ODMyOEVBMDU5MjA1QzBBNUM0MzkyRDM5MDU2OUExQjZBRDRBQTNGQkUifX0=
2025-06-11 14:39:36 | INFO     | services.airtest_poco_service:execute_poco_query:691 - 执行Poco语句: self.poco("test")
2025-06-11 14:39:37 | INFO     | services.airtest_poco_service:execute_poco_query:699 - Poco语句执行成功: 元素不存在
2025-06-11 14:39:37 | DEBUG    | services.debug_service:add_command_history:33 - 添加poco命令历史记录: poco("test")
2025-06-11 14:41:22 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_96359xfbp_1749623719218 断开连接
2025-06-11 14:41:36 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -d -t 500
2025-06-11 14:41:36 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 61397
2025-06-11 14:41:47 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: logcat -c
2025-06-11 14:41:48 | INFO     | services.airtest_poco_service:execute_adb_command:660 - ADB命令执行成功，输出长度: 12
2025-06-11 14:42:16 | INFO     | services.airtest_poco_service:execute_adb_command:647 - 执行ADB命令: adb devices
2025-06-11 14:42:16 | ERROR    | services.airtest_poco_service:execute_adb_command:665 - 执行ADB命令 'adb devices' 失败: stdout[b''] stderr[b'/system/bin/sh: adb: not found\n']
2025-06-11 14:42:16 | ERROR    | api.debug_router:execute_adb_command:53 - 执行ADB命令失败: 执行ADB命令 'adb devices' 失败: stdout[b''] stderr[b'/system/bin/sh: adb: not found\n']
2025-06-11 14:42:16 | DEBUG    | services.debug_service:add_command_history:33 - 添加adb命令历史记录: adb devices
2025-06-11 14:42:33 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_eazxna8cs_1749624088438 断开连接
2025-06-11 14:42:43 | INFO     | main:websocket_endpoint:110 - WebSocket客户端 client_eazxna8cs_1749624088438 断开连接
2025-06-11 14:42:53 | INFO     | __main__:<module>:123 - 启动DebugTools后端服务...
