"""
调试工具API路由
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Any
from datetime import datetime
from loguru import logger

from services.device_service import DeviceService
from services.debug_service import DebugService

router = APIRouter()

# 全局服务实例
device_service = DeviceService()
debug_service = DebugService()

class AdbCommandRequest(BaseModel):
    command: str

class PocoQueryRequest(BaseModel):
    query: str

class CommandHistoryItem(BaseModel):
    command: str
    result: str
    timestamp: str
    command_type: str  # "adb" or "poco"

@router.post("/adb/execute")
async def execute_adb_command(request: AdbCommandRequest):
    """执行ADB命令"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        result = await operation.execute_adb_command(request.command)
        
        # 保存到历史记录
        debug_service.add_command_history("adb", request.command, result)
        
        return {
            "data": {
                "command": request.command,
                "result": result,
                "success": True
            }
        }
    except Exception as e:
        logger.error(f"执行ADB命令失败: {e}")
        error_msg = str(e)
        
        # 保存错误到历史记录
        debug_service.add_command_history("adb", request.command, f"错误: {error_msg}")
        
        return {
            "data": {
                "command": request.command,
                "result": f"错误: {error_msg}",
                "success": False
            }
        }

@router.post("/poco/execute")
async def execute_poco_query(request: PocoQueryRequest):
    """执行Poco查询"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        result = await operation.execute_poco_query(request.query)
        
        # 将结果转换为字符串
        result_str = str(result) if result is not None else "None"
        
        # 保存到历史记录
        debug_service.add_command_history("poco", request.query, result_str)
        
        return {
            "data": {
                "query": request.query,
                "result": result_str,
                "success": True
            }
        }
    except Exception as e:
        logger.error(f"执行Poco查询失败: {e}")
        error_msg = str(e)
        
        # 保存错误到历史记录
        debug_service.add_command_history("poco", request.query, f"错误: {error_msg}")
        
        return {
            "data": {
                "query": request.query,
                "result": f"错误: {error_msg}",
                "success": False
            }
        }

@router.get("/adb/history")
async def get_adb_history():
    """获取ADB命令历史记录"""
    try:
        history = debug_service.get_command_history("adb")
        return {"data": history}
    except Exception as e:
        logger.error(f"获取ADB历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/poco/history")
async def get_poco_history():
    """获取Poco查询历史记录"""
    try:
        history = debug_service.get_command_history("poco")
        return {"data": history}
    except Exception as e:
        logger.error(f"获取Poco历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/history", response_model=List[CommandHistoryItem])
async def get_all_history():
    """获取所有命令历史记录"""
    try:
        history = debug_service.get_all_command_history()
        return history
    except Exception as e:
        logger.error(f"获取历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/history/clear")
async def clear_history():
    """清空历史记录"""
    try:
        debug_service.clear_command_history()
        return {"message": "历史记录已清空"}
    except Exception as e:
        logger.error(f"清空历史记录失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/logcat")
async def get_device_logs():
    """获取设备日志"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")

        # 获取最近的logcat日志，增加更多行数
        logs = await operation.execute_adb_command("logcat -d -t 500")

        return {
            "data": {
                "logs": logs,
                "timestamp": datetime.now().isoformat(),
                "message": "获取设备日志成功"
            }
        }
    except Exception as e:
        logger.error(f"获取设备日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/logcat/realtime")
async def get_realtime_device_logs():
    """获取实时设备日志"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")

        # 获取实时日志（最近10行）
        logs = await operation.execute_adb_command("logcat -d -t 10")

        return {
            "data": {
                "logs": logs,
                "timestamp": datetime.now().isoformat(),
                "message": "获取实时设备日志成功"
            }
        }
    except Exception as e:
        logger.error(f"获取实时设备日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/logcat/clear")
async def clear_device_logs():
    """清空设备日志"""
    try:
        operation = device_service.get_current_operation()
        if not operation:
            raise HTTPException(status_code=400, detail="没有连接的设备")
        
        result = await operation.execute_adb_command("logcat -c")
        
        return {
            "message": "设备日志已清空",
            "result": result
        }
    except Exception as e:
        logger.error(f"清空设备日志失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
