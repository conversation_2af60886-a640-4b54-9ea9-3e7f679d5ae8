#!/usr/bin/env python3
"""
WebSocket连接测试脚本
用于测试流媒体WebSocket连接
"""

import asyncio
import websockets
import json
import base64
import time

async def test_websocket_connection():
    """测试WebSocket连接"""
    uri = "ws://localhost:8000/api/streaming/stream"
    
    print(f"连接到WebSocket: {uri}")
    
    try:
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            frame_count = 0
            start_time = time.time()
            
            # 监听消息
            async for message in websocket:
                try:
                    data = json.loads(message)
                    message_type = data.get('type', 'unknown')
                    
                    if message_type == 'connection':
                        print(f"📡 连接确认: {data.get('message', '')}")
                    
                    elif message_type == 'frame':
                        frame_count += 1
                        timestamp = data.get('timestamp', 0)
                        data_size = len(data.get('data', ''))
                        
                        print(f"🖼️  帧 #{frame_count}: 时间戳={timestamp:.3f}, 数据大小={data_size}")
                        
                        # 每10帧显示一次统计
                        if frame_count % 10 == 0:
                            elapsed = time.time() - start_time
                            fps = frame_count / elapsed if elapsed > 0 else 0
                            print(f"📊 统计: {frame_count} 帧, {elapsed:.1f}秒, {fps:.1f} FPS")
                        
                        # 测试30帧后退出
                        if frame_count >= 30:
                            print("✅ 测试完成，收到30帧数据")
                            break
                    
                    else:
                        print(f"❓ 未知消息类型: {message_type}")
                
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                except Exception as e:
                    print(f"❌ 处理消息失败: {e}")
            
            print(f"🎉 WebSocket测试完成，总共收到 {frame_count} 帧")
            
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket连接关闭: {e}")
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")

async def main():
    """主函数"""
    print("DebugTools WebSocket连接测试")
    print("=" * 50)
    
    # 首先检查流媒体状态
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            # 检查WebSocket端点状态
            async with session.get('http://localhost:8000/api/streaming/test-websocket') as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print("📋 流媒体状态:")
                    status = data.get('streaming_status', {})
                    print(f"   - 流媒体运行: {status.get('is_streaming', False)}")
                    print(f"   - 设备ID: {status.get('device_id', 'None')}")
                    print(f"   - 有帧数据: {status.get('latest_frame_available', False)}")
                    print(f"   - 客户端数: {status.get('websocket_clients', 0)}")
                    print()
                    
                    if not status.get('latest_frame_available', False):
                        print("⚠️  没有可用的帧数据，请先启动流媒体")
                        print("   执行: curl -X POST 'http://localhost:8000/api/streaming/start' \\")
                        print("              -H 'Content-Type: application/json' \\")
                        print("              -d '{\"device_id\": \"3ef2ce8b\", \"fps\": 10, \"quality\": 80, \"scale\": 1.0}'")
                        return
                else:
                    print(f"❌ 无法获取流媒体状态: HTTP {resp.status}")
                    return
    
    except Exception as e:
        print(f"❌ 检查流媒体状态失败: {e}")
        return
    
    # 测试WebSocket连接
    await test_websocket_connection()

if __name__ == "__main__":
    asyncio.run(main())
