"""
Airtest + Poco 实现的设备操作服务
"""

import os
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import random

from airtest.core.api import connect_device, snapshot, touch, text, swipe, set_logdir
from airtest.core.android.adb import ADB
from poco.drivers.android.uiautomation import AndroidUiautomationPoco

from core.device_operations import DeviceOperationInterface, DeviceInfo, ElementInfo
from core.config import settings
from loguru import logger

class AirtestPocoService(DeviceOperationInterface):
    """Airtest + Poco 设备操作服务实现"""
    
    def __init__(self):
        self.device = None
        self.poco = None
        self.adb = None
        self.current_device_id = None
        self.cached_dom_tree = None
        self.cached_screenshot_path = None
        self.element_colors = {}  # 存储元素框选颜色
        
    async def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        try:
            logger.info(f"正在连接设备: {device_id}")

            # 设置airtest日志目录
            set_logdir(settings.SCREENSHOT_DIR)

            # 连接设备
            self.device = connect_device(f"Android:///{device_id}")
            self.adb = ADB(device_id)

            # 初始化Poco
            self.poco = AndroidUiautomationPoco(device=self.device, use_airtest_input=True)

            self.current_device_id = device_id

            # 清除缓存
            self.cached_dom_tree = None
            self.cached_screenshot_path = None

            logger.info(f"设备 {device_id} 连接成功")
            return True

        except Exception as e:
            logger.error(f"连接设备 {device_id} 失败: {e}")
            return False
    
    async def disconnect_device(self) -> bool:
        """断开设备连接"""
        try:
            if self.device:
                # Airtest没有显式的断开连接方法，只需要清空引用
                self.device = None
                self.poco = None
                self.adb = None
                self.current_device_id = None
                self.cached_dom_tree = None
                self.cached_screenshot_path = None
                
                logger.info("设备连接已断开")
                return True
        except Exception as e:
            logger.error(f"断开设备连接失败: {e}")
            return False
    
    async def get_device_info(self) -> Optional[DeviceInfo]:
        """获取设备信息"""
        if not self.device or not self.adb:
            return None
            
        try:
            # 获取设备基本信息
            device_info = self.adb.shell("getprop")
            
            # 解析设备信息
            name = self._extract_prop_value(device_info, "ro.product.model")
            model = self._extract_prop_value(device_info, "ro.product.device")
            version = self._extract_prop_value(device_info, "ro.build.version.release")
            
            # 获取屏幕分辨率
            screen_info = self.adb.shell("wm size")
            resolution = self._parse_resolution(screen_info)
            
            # 获取屏幕密度
            density_info = self.adb.shell("wm density")
            density = self._parse_density(density_info)
            
            # 计算屏幕物理尺寸（简单估算）
            if resolution and density:
                width_inch = resolution[0] / density
                height_inch = resolution[1] / density
                screen_size = (width_inch, height_inch)
            else:
                screen_size = (0.0, 0.0)
            
            return DeviceInfo(
                device_id=self.current_device_id,
                name=name or "Unknown",
                model=model or "Unknown",
                version=version or "Unknown",
                resolution=resolution or (0, 0),
                screen_size=screen_size,
                status="connected"
            )
            
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return None
    
    def _extract_prop_value(self, prop_output: str, prop_name: str) -> str:
        """从getprop输出中提取属性值"""
        for line in prop_output.split('\n'):
            if prop_name in line:
                # 格式: [prop_name]: [value]
                parts = line.split(': ')
                if len(parts) >= 2:
                    return parts[1].strip('[]')
        return ""
    
    def _parse_resolution(self, size_output: str) -> Optional[Tuple[int, int]]:
        """解析分辨率信息"""
        try:
            # 格式: Physical size: 1080x2340
            for line in size_output.split('\n'):
                if 'Physical size:' in line:
                    size_str = line.split(': ')[1]
                    width, height = map(int, size_str.split('x'))
                    return (width, height)
        except:
            pass
        return None
    
    def _parse_density(self, density_output: str) -> Optional[int]:
        """解析屏幕密度"""
        try:
            # 格式: Physical density: 420
            for line in density_output.split('\n'):
                if 'Physical density:' in line:
                    density = int(line.split(': ')[1])
                    return density
        except:
            pass
        return None

    async def take_screenshot(self, mark_elements: bool = False) -> str:
        """截图"""
        if not self.device:
            raise Exception("设备未连接")

        try:
            # 生成截图文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            filepath = os.path.join(settings.SCREENSHOT_DIR, filename)

            # 使用airtest截图
            snapshot(filename=filename)

            if mark_elements:
                # 如果需要标记元素，强制重新获取DOM树以确保最新状态
                logger.info("标记元素模式：正在获取DOM树...")
                await self.get_dom_tree()

                # 在截图上标记元素
                logger.info("正在标记元素...")
                await self._mark_elements_on_screenshot(filepath)

            self.cached_screenshot_path = filepath
            logger.info(f"截图保存至: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"截图失败: {e}")
            raise

    def _generate_random_color(self, seq_index):
        """根据seq_index生成随机但一致的颜色"""
        import colorsys
        # 使用seq_index作为种子，确保相同的seq_index总是生成相同的颜色
        random.seed(hash(str(seq_index)) % 2147483647)

        # 生成高饱和度、中等亮度的颜色，确保视觉效果好
        hue = random.random()  # 色相：0-1
        saturation = 0.7 + random.random() * 0.3  # 饱和度：0.7-1.0
        lightness = 0.4 + random.random() * 0.3   # 亮度：0.4-0.7

        # 转换HSL到RGB
        rgb = colorsys.hls_to_rgb(hue, lightness, saturation)
        return tuple(int(c * 255) for c in rgb)

    def _get_contrasting_text_color(self, bg_color):
        """根据背景颜色获取对比度高的文本颜色"""
        # 计算背景颜色的亮度
        r, g, b = bg_color[:3]  # 只取RGB值，忽略alpha
        brightness = (r * 299 + g * 587 + b * 114) / 1000

        # 如果背景较暗，使用白色文字；如果背景较亮，使用黑色文字
        return (255, 255, 255) if brightness < 128 else (0, 0, 0)

    def _draw_node_bounding_box(self, draw, node, screen_width, screen_height, font):
        """递归地为每个节点绘制边界框"""
        # 检查节点是否有位置和大小信息
        if "payload" in node and "pos" in node["payload"] and "size" in node["payload"]:
            # 获取节点的位置和大小
            pos = node["payload"]["pos"]
            size = node["payload"]["size"]

            # 计算边界框坐标
            x1 = int((pos[0] - size[0]/2) * screen_width)
            y1 = int((pos[1] - size[1]/2) * screen_height)
            x2 = int((pos[0] + size[0]/2) * screen_width)
            y2 = int((pos[1] + size[1]/2) * screen_height)

            # 获取seq_index用于生成颜色和标签
            seq_index = node.get("seq_index", "")

            # 构建标签文本
            label_text = f"{seq_index}"

            # 生成随机颜色
            border_color = self._generate_random_color(seq_index)

            # 绘制边界框 - 使用随机颜色，增加线条宽度以提高可见性
            draw.rectangle([x1, y1, x2, y2], outline=border_color, width=3)

            # 在边界框顶部绘制标签
            if label_text:
                try:
                    # 限制标签文本总长度
                    if len(label_text) > 25:
                        label_text = label_text[:22] + "..."

                    # 使用字体计算实际文本大小
                    try:
                        bbox = draw.textbbox((0, 0), label_text, font=font)
                        text_width = bbox[2] - bbox[0] + 6  # 添加padding
                        text_height = bbox[3] - bbox[1] + 4  # 添加padding
                    except:
                        # 如果textbbox不可用，使用估算
                        text_width = len(label_text) * 8 + 6
                        text_height = 16

                    # 确保标签不会超出屏幕边界
                    label_x = x1
                    if label_x + text_width > screen_width:
                        label_x = screen_width - text_width
                    if label_x < 0:
                        label_x = 0

                    # 确保标签不会超出屏幕顶部
                    label_y = y1 - text_height
                    if label_y < 0:
                        label_y = y2  # 如果顶部放不下，放到底部
                        if label_y + text_height > screen_height:
                            label_y = screen_height - text_height

                    # 创建标签背景颜色（使用边界框颜色的半透明版本）
                    bg_color = border_color + (200,)  # 添加alpha通道

                    # 绘制标签背景
                    draw.rectangle(
                        [label_x, label_y, label_x + text_width, label_y + text_height],
                        fill=bg_color
                    )

                    # 获取对比度高的文本颜色
                    text_color = self._get_contrasting_text_color(border_color)

                    # 绘制文本
                    draw.text((label_x + 3, label_y + 2), label_text, fill=text_color, font=font)

                except Exception as e:
                    logger.error(f"绘制标签失败: {str(e)}")

        # 递归处理子节点
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._draw_node_bounding_box(draw, child, screen_width, screen_height, font)

    async def _mark_elements_on_screenshot(self, screenshot_path: str):
        """在截图上绘制边界框"""
        try:
            # 打开截图
            image = Image.open(screenshot_path)
            draw = ImageDraw.Draw(image, 'RGBA')  # 使用RGBA模式以支持透明度

            # 获取屏幕分辨率
            width, height = image.size

            # 加载字体
            try:
                font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 20)
            except:
                font = ImageFont.load_default()

            # 重置随机种子，确保颜色生成的一致性
            random.seed(42)

            # 递归绘制边界框
            if self.cached_dom_tree and 'root' in self.cached_dom_tree:
                root = self.cached_dom_tree['root']
                self._draw_node_bounding_box(draw, root, width, height, font)
                logger.info("成功在截图上标记元素")
            else:
                logger.warning("没有可用的DOM树数据进行标记")

            # 保存标记后的截图
            image.save(screenshot_path)
            logger.info(f"标记后的截图已保存: {screenshot_path}")

        except Exception as e:
            logger.error(f"标记元素失败: {e}")
            raise

    async def get_dom_tree(self) -> Dict[str, Any]:
        """获取DOM树"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            logger.info("正在获取DOM树...")

            # 获取根节点
            root = self.poco.agent.hierarchy.dump()

            # 添加层级索引到DOM树
            self._add_index_to_dom_tree(root)

            # 添加全局顺序索引
            self._add_sequential_index(root)

            # 处理DOM树并提取元素信息
            elements = []
            self._process_dom_node(root, elements)

            dom_tree = {
                "timestamp": datetime.now().isoformat(),
                "total_elements": len(elements),
                "elements": elements,
                "root": root  # 保存原始DOM树用于标记
            }

            # 缓存DOM树
            self.cached_dom_tree = dom_tree

            logger.info(f"DOM树获取成功，共 {len(elements)} 个元素")
            return dom_tree

        except Exception as e:
            logger.error(f"获取DOM树失败: {e}")
            raise

    def _add_index_to_dom_tree(self, node, parent_index="", current_index=0):
        """递归地为DOM树添加层级索引"""
        # 为当前节点添加层级索引
        index = f"{parent_index}{current_index}" if parent_index else f"{current_index}"
        node["index"] = index

        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for i, child in enumerate(node["children"]):
                self._add_index_to_dom_tree(child, f"{index}_", i)

    def _add_sequential_index(self, node, index_map=None):
        """为DOM树添加全局顺序索引"""
        if index_map is None:
            index_map = {"current": 0}

        # 为当前节点添加顺序索引
        node["seq_index"] = index_map["current"]
        index_map["current"] += 1

        # 如果有子节点，递归处理
        if "children" in node and node["children"]:
            for child in node["children"]:
                self._add_sequential_index(child, index_map)

    def _process_dom_node(self, node: dict, elements: list):
        """递归处理DOM节点，提取元素信息"""
        try:
            # 提取节点信息
            payload = node.get('payload', {})

            # 处理位置和大小信息
            pos = payload.get('pos', [0, 0])
            size = payload.get('size', [0, 0])
            bounds = payload.get('bounds', [])

            # 如果有bounds信息，优先使用bounds计算pos和size
            if bounds and len(bounds) == 4:
                # bounds格式: [left, top, right, bottom]
                left, top, right, bottom = bounds
                pos = [(left + right) // 2, (top + bottom) // 2]  # 中心点
                size = [right - left, bottom - top]  # 宽高

            element_info = {
                'index': node.get('index', ''),  # 层级索引
                'seq_index': node.get('seq_index', 0),  # 全局顺序索引
                'name': payload.get('name', ''),
                'text': payload.get('text', ''),
                'pos': pos,
                'size': size,
                'visible': payload.get('visible', False),
                'enabled': payload.get('enabled', False),
                'attributes': {
                    'class': payload.get('type', ''),
                    'resource_id': payload.get('desc', ''),
                    'package': payload.get('package', ''),
                    'bounds': bounds
                }
            }

            elements.append(element_info)

            # 递归处理子节点
            children = node.get('children', [])
            for child in children:
                self._process_dom_node(child, elements)

        except Exception as e:
            logger.error(f"处理DOM节点失败: {e}")

    async def click_by_coordinates(self, x: int, y: int) -> bool:
        """通过坐标点击"""
        if not self.device:
            raise Exception("设备未连接")

        try:
            touch((x, y))
            logger.info(f"点击坐标 ({x}, {y}) 成功")
            return True
        except Exception as e:
            logger.error(f"点击坐标 ({x}, {y}) 失败: {e}")
            return False

    async def click_by_seq_index(self, seq_index: str) -> bool:
        """通过seq_index点击"""
        if not self.cached_dom_tree:
            await self.get_dom_tree()

        try:
            # 查找对应的元素
            element = self._find_element_by_seq_index(seq_index)
            if not element:
                logger.error(f"未找到seq_index为 {seq_index} 的元素")
                return False

            pos = element.get('pos', [0, 0])
            if pos[0] > 0 and pos[1] > 0:
                return await self.click_by_coordinates(pos[0], pos[1])
            else:
                logger.error(f"元素 {seq_index} 位置信息无效")
                return False

        except Exception as e:
            logger.error(f"通过seq_index {seq_index} 点击失败: {e}")
            return False

    async def click_by_poco_query(self, poco_query: str) -> bool:
        """通过poco语句点击"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            # 执行poco查询并点击
            element = eval(f"self.poco{poco_query}")
            element.click()
            logger.info(f"通过poco语句 {poco_query} 点击成功")
            return True
        except Exception as e:
            logger.error(f"通过poco语句 {poco_query} 点击失败: {e}")
            return False

    def _find_element_by_seq_index(self, seq_index: str) -> Optional[dict]:
        """根据seq_index查找元素"""
        if not self.cached_dom_tree or 'elements' not in self.cached_dom_tree:
            return None

        try:
            # 将seq_index转换为整数进行比较
            target_seq_index = int(seq_index)
        except ValueError:
            logger.error(f"无效的seq_index格式: {seq_index}")
            return None

        for element in self.cached_dom_tree['elements']:
            if element.get('seq_index') == target_seq_index:
                return element
        return None

    async def input_text_by_coordinates(self, x: int, y: int, input_text: str, press_enter: bool = False) -> bool:
        """通过坐标输入文本"""
        try:
            # 先点击坐标
            await self.click_by_coordinates(x, y)
            # 等待一下再输入
            await asyncio.sleep(0.5)
            # 输入文本
            text(input_text)
            # 如果需要按回车
            if press_enter:
                await asyncio.sleep(0.2)
                from airtest.core.api import keyevent
                keyevent("ENTER")
                logger.info(f"在坐标 ({x}, {y}) 输入文本 '{input_text}' 并按回车成功")
            else:
                logger.info(f"在坐标 ({x}, {y}) 输入文本 '{input_text}' 成功")
            return True
        except Exception as e:
            logger.error(f"在坐标 ({x}, {y}) 输入文本失败: {e}")
            return False

    async def input_text_by_seq_index(self, seq_index: str, input_text: str, press_enter: bool = False) -> bool:
        """通过seq_index输入文本"""
        if not self.cached_dom_tree:
            await self.get_dom_tree()

        try:
            element = self._find_element_by_seq_index(seq_index)
            if not element:
                logger.error(f"未找到seq_index为 {seq_index} 的元素")
                return False

            pos = element.get('pos', [0, 0])
            if pos[0] > 0 and pos[1] > 0:
                return await self.input_text_by_coordinates(pos[0], pos[1], input_text, press_enter)
            else:
                logger.error(f"元素 {seq_index} 位置信息无效")
                return False

        except Exception as e:
            logger.error(f"通过seq_index {seq_index} 输入文本失败: {e}")
            return False

    async def input_text_by_poco_query(self, poco_query: str, input_text: str, press_enter: bool = False) -> bool:
        """通过poco语句输入文本"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            element = eval(f"self.poco{poco_query}")
            element.set_text(input_text)
            # 如果需要按回车
            if press_enter:
                await asyncio.sleep(0.2)
                from airtest.core.api import keyevent
                keyevent("ENTER")
                logger.info(f"通过poco语句 {poco_query} 输入文本 '{input_text}' 并按回车成功")
            else:
                logger.info(f"通过poco语句 {poco_query} 输入文本 '{input_text}' 成功")
            return True
        except Exception as e:
            logger.error(f"通过poco语句 {poco_query} 输入文本失败: {e}")
            return False

    async def swipe(self, direction: str = "up", distance: float = 0.3) -> bool:
        """滑动操作"""
        if not self.device:
            raise Exception("设备未连接")

        try:
            if direction == "up":
                start_pos = (0.5, 0.8)
                end_pos = (0.5, 0.8 - distance)
            elif direction == "down":
                start_pos = (0.5, 0.2)
                end_pos = (0.5, 0.2 + distance)
            elif direction == "left":
                start_pos = (0.8, 0.5)
                end_pos = (0.8 - distance, 0.5)
            elif direction == "right":
                start_pos = (0.2, 0.5)
                end_pos = (0.2 + distance, 0.5)
            else:
                logger.error(f"不支持的滑动方向: {direction}")
                return False

            swipe(start_pos, end_pos)
            logger.info(f"向 {direction} 滑动成功")
            return True
        except Exception as e:
            logger.error(f"滑动操作失败: {e}")
            return False

    async def swipe_by_poco_query(self, poco_query: str, direction: str = "up") -> bool:
        """通过poco语句滑动"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            element = eval(f"self.poco{poco_query}")
            if direction == "up":
                element.swipe("up")
            elif direction == "down":
                element.swipe("down")
            elif direction == "left":
                element.swipe("left")
            elif direction == "right":
                element.swipe("right")
            else:
                logger.error(f"不支持的滑动方向: {direction}")
                return False

            logger.info(f"通过poco语句 {poco_query} 向 {direction} 滑动成功")
            return True
        except Exception as e:
            logger.error(f"通过poco语句滑动失败: {e}")
            return False

    async def assert_element_exists(self, poco_query: str) -> bool:
        """断言元素是否存在"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            element = eval(f"self.poco{poco_query}")
            exists = element.exists()
            logger.info(f"断言元素 {poco_query} 存在: {exists}")
            return exists
        except Exception as e:
            logger.error(f"断言元素存在失败: {e}")
            return False

    async def execute_adb_command(self, command: str) -> str:
        """执行ADB命令"""
        if not self.adb:
            raise Exception("设备未连接")

        try:
            # 清理命令
            command = command.strip()

            # 记录执行的命令
            logger.info(f"执行ADB命令: {command}")

            # 解析命令类型并执行
            result = self._execute_adb_command_by_type(command)

            # 处理结果
            if result is None:
                result = "命令执行完成（无输出）"
            elif isinstance(result, str):
                result = result.strip()
                if not result:
                    result = "命令执行完成（输出为空）"

            logger.info(f"ADB命令执行成功，输出长度: {len(str(result))}")
            return result

        except Exception as e:
            error_msg = f"执行ADB命令 '{command}' 失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def _get_adb_path(self) -> str:
        """获取ADB可执行文件路径"""
        import shutil

        # 首先尝试使用当前ADB实例的路径
        if hasattr(self.adb, 'adb_path') and self.adb.adb_path:
            logger.info(f"使用ADB实例路径: {self.adb.adb_path}")
            return self.adb.adb_path

        # 尝试从airtest包中查找adb
        try:
            import airtest
            airtest_path = os.path.dirname(airtest.__file__)

            # 常见的airtest adb路径
            possible_paths = [
                os.path.join(airtest_path, 'core', 'android', 'static', 'adb', 'linux', 'adb'),
                os.path.join(airtest_path, 'core', 'android', 'static', 'adb', 'mac', 'adb'),
                os.path.join(airtest_path, 'core', 'android', 'static', 'adb', 'windows', 'adb.exe'),
                os.path.join(airtest_path, '..', 'airtest', 'core', 'android', 'static', 'adb', 'linux', 'adb'),
                os.path.join(airtest_path, '..', 'airtest', 'core', 'android', 'static', 'adb', 'mac', 'adb'),
                os.path.join(airtest_path, '..', 'airtest', 'core', 'android', 'static', 'adb', 'windows', 'adb.exe'),
            ]

            for path in possible_paths:
                if os.path.exists(path) and os.access(path, os.X_OK):
                    logger.info(f"找到airtest内置ADB: {path}")
                    return path
        except Exception as e:
            logger.warning(f"查找airtest内置ADB失败: {e}")

        # 尝试从系统PATH获取
        adb_path = shutil.which('adb')
        if adb_path:
            logger.info(f"使用系统PATH中的ADB: {adb_path}")
            return adb_path

        # 最后尝试默认路径
        logger.warning("未找到ADB路径，使用默认'adb'")
        return 'adb'

    def _execute_adb_command_by_type(self, command: str) -> str:
        """根据命令类型执行不同的ADB操作"""
        import subprocess

        # 移除可能的adb前缀，统一处理
        if command.startswith('adb '):
            command = command[4:].strip()

        # 获取ADB可执行文件路径
        adb_path = self._get_adb_path()
        logger.info(f"使用ADB路径: {adb_path}")

        # 获取设备序列号
        device_serial = None
        if hasattr(self.adb, 'serialno'):
            device_serial = self.adb.serialno
        elif hasattr(self.adb, 'device_id'):
            device_serial = self.adb.device_id

        # 特殊处理一些命令类型
        if command.startswith('shell '):
            # shell命令，使用airtest的shell方法
            shell_cmd = command[6:].strip()
            return self.adb.shell(shell_cmd)
        elif command in ['devices', 'version', 'help']:
            # 全局命令，不需要设备序列号
            if device_serial:
                full_command = [adb_path, command]
            else:
                full_command = [adb_path, command]
        elif command.startswith(('install ', 'uninstall ', 'push ', 'pull ')):
            # 文件操作命令，使用完整命令
            if device_serial:
                full_command = [adb_path, '-s', device_serial] + command.split()
            else:
                full_command = [adb_path] + command.split()
        elif command.startswith(('logcat', 'bugreport', 'dumpsys')):
            # 系统信息命令
            if device_serial:
                full_command = [adb_path, '-s', device_serial] + command.split()
            else:
                full_command = [adb_path] + command.split()
        else:
            # 默认作为shell命令处理
            return self.adb.shell(command)

        # 执行完整的adb命令
        try:
            logger.info(f"执行命令: {' '.join(full_command)}")
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                return result.stdout
            else:
                error_output = result.stderr or result.stdout
                raise Exception(f"命令执行失败 (返回码: {result.returncode}): {error_output}")

        except subprocess.TimeoutExpired:
            raise Exception("命令执行超时（30秒）")
        except Exception as e:
            raise Exception(f"命令执行出错: {str(e)}")

    async def execute_poco_query(self, poco_query: str) -> Any:
        """执行Poco语句"""
        if not self.poco:
            raise Exception("设备未连接")

        try:
            # 清理输入的poco语句
            poco_query = poco_query.strip()

            # 检查是否已经包含poco前缀，避免重复
            if poco_query.startswith('poco(') or poco_query.startswith('self.poco('):
                # 如果已经包含poco前缀，直接执行
                if poco_query.startswith('self.poco('):
                    exec_query = poco_query
                else:
                    # 将poco替换为self.poco
                    exec_query = poco_query.replace('poco(', 'self.poco(', 1)
            else:
                # 如果没有poco前缀，添加self.poco前缀
                if not poco_query.startswith('('):
                    poco_query = f"({poco_query})" if not poco_query.startswith('.') else poco_query
                exec_query = f"self.poco{poco_query}"

            logger.info(f"执行Poco语句: {exec_query}")

            # 执行poco语句
            result = eval(exec_query)

            # 处理不同类型的结果
            result_info = self._process_poco_result(result, exec_query)

            logger.info(f"Poco语句执行成功: {result_info}")
            return result_info

        except Exception as e:
            error_msg = f"执行Poco语句 '{poco_query}' 失败: {e}"
            logger.error(error_msg)
            raise Exception(error_msg)

    def _process_poco_result(self, result, query: str) -> str:
        """处理Poco执行结果，返回有意义的信息"""
        try:
            # 如果是点击、输入等操作，返回操作结果
            if '.click()' in query:
                return f"点击操作执行成功"
            elif '.set_text(' in query:
                return f"文本输入操作执行成功"
            elif '.swipe(' in query:
                return f"滑动操作执行成功"
            elif '.exists()' in query:
                return f"元素存在性检查: {result}"
            elif '.get_text()' in query:
                return f"获取文本: {result}"
            elif '.attr(' in query:
                return f"获取属性: {result}"
            elif '.wait(' in query:
                return f"等待操作执行成功"
            else:
                # 对于查询操作，尝试获取元素信息
                if hasattr(result, 'exists'):
                    exists = result.exists()
                    if exists:
                        info = []
                        try:
                            if hasattr(result, 'get_text'):
                                text = result.get_text()
                                if text:
                                    info.append(f"文本: {text}")
                        except:
                            pass

                        try:
                            if hasattr(result, 'attr'):
                                name = result.attr('name')
                                if name:
                                    info.append(f"名称: {name}")
                        except:
                            pass

                        try:
                            if hasattr(result, 'get_position'):
                                pos = result.get_position()
                                info.append(f"位置: {pos}")
                        except:
                            pass

                        if info:
                            return f"找到元素 - {', '.join(info)}"
                        else:
                            return "找到元素"
                    else:
                        return "元素不存在"
                else:
                    # 直接返回结果的字符串表示
                    return str(result) if result is not None else "操作完成"

        except Exception as e:
            logger.warning(f"处理Poco结果时出错: {e}")
            return str(result) if result is not None else "操作完成"

# 注册实现到工厂
from core.device_operations import DeviceOperationFactory
DeviceOperationFactory.register_implementation("airtest_poco", AirtestPocoService)
