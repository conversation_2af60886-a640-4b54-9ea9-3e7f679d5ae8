"""
Android设备屏幕实时流媒体服务
支持实时屏幕展示、录制视频、截图等功能
"""

import asyncio
import subprocess
import threading
import time
import base64
import json
import os
import signal
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class ScreenStreamingService:
    def __init__(self):
        self.device_id: Optional[str] = None
        self.is_streaming = False
        self.is_recording = False
        self.stream_process: Optional[subprocess.Popen] = None
        self.record_process: Optional[subprocess.Popen] = None
        self.websocket_clients: List = []
        self.stream_thread: Optional[threading.Thread] = None
        self.fps = 20  # 默认20fps，提高流畅度
        self.quality = 60  # JPEG质量 1-100，平衡质量和速度
        self.scale = 0.8  # 缩放比例，保持较好的清晰度
        self.latest_frame = None  # 存储最新帧数据
        
        # 录制相关
        self.recording_dir = "recordings"
        self.screenshots_dir = "screenshots/streaming"
        
        # 确保目录存在
        os.makedirs(self.recording_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    def set_device(self, device_id: str):
        """设置目标设备"""
        self.device_id = device_id
        logger.info(f"设置流媒体目标设备: {device_id}")
    
    def add_websocket_client(self, websocket):
        """添加WebSocket客户端"""
        self.websocket_clients.append(websocket)
        logger.info(f"添加WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    def remove_websocket_client(self, websocket):
        """移除WebSocket客户端"""
        if websocket in self.websocket_clients:
            self.websocket_clients.remove(websocket)
        logger.info(f"移除WebSocket客户端，当前客户端数: {len(self.websocket_clients)}")
    
    async def broadcast_to_clients(self, message: Dict[str, Any]):
        """向所有WebSocket客户端广播消息"""
        if not self.websocket_clients:
            return
        
        message_str = json.dumps(message)
        disconnected_clients = []
        
        for client in self.websocket_clients:
            try:
                await client.send_text(message_str)
            except Exception as e:
                logger.warning(f"向客户端发送消息失败: {e}")
                disconnected_clients.append(client)
        
        # 移除断开连接的客户端
        for client in disconnected_clients:
            self.remove_websocket_client(client)

    def _send_message_sync(self, message: Dict[str, Any]):
        """同步发送消息的辅助方法"""
        import asyncio

        async def send():
            await self.broadcast_to_clients(message)

        # 创建新的事件循环来发送消息
        loop = asyncio.new_event_loop()
        try:
            loop.run_until_complete(send())
        finally:
            loop.close()
    
    def start_streaming(self, fps: int = 20, quality: int = 60, scale: float = 0.8, mode: str = 'traditional') -> bool:
        """开始屏幕流媒体"""
        if not self.device_id:
            logger.error("未设置设备ID")
            return False

        if self.is_streaming:
            logger.warning("流媒体已在运行")
            return True

        # 验证设备连接
        try:
            test_cmd = ['adb', '-s', self.device_id, 'shell', 'echo', 'test']
            result = subprocess.run(test_cmd, capture_output=True, timeout=5)
            if result.returncode != 0:
                logger.error(f"设备 {self.device_id} 连接测试失败: {result.stderr.decode()}")
                return False
            logger.info(f"设备 {self.device_id} 连接正常")
        except Exception as e:
            logger.error(f"设备连接验证失败: {e}")
            return False

        self.fps = fps
        self.quality = quality
        self.scale = scale
        self.mode = mode  # 添加模式标记

        try:
            # 启动流媒体线程
            self.is_streaming = True
            self.stream_thread = threading.Thread(target=self._stream_worker, daemon=True)
            self.stream_thread.start()

            mode_name = "FFmpeg优化" if mode == 'ffmpeg' else "传统"
            logger.info(f"开始{mode_name}屏幕流媒体，FPS: {fps}, 质量: {quality}, 缩放: {scale}")
            return True
        except Exception as e:
            logger.error(f"启动流媒体失败: {e}")
            self.is_streaming = False
            return False
    
    def stop_streaming(self):
        """停止屏幕流媒体"""
        self.is_streaming = False
        
        if self.stream_process:
            try:
                self.stream_process.terminate()
                self.stream_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.stream_process.kill()
            except Exception as e:
                logger.warning(f"停止流媒体进程失败: {e}")
            finally:
                self.stream_process = None
        
        if self.stream_thread:
            self.stream_thread.join(timeout=5)
            self.stream_thread = None
        
        logger.info("屏幕流媒体已停止")
    
    def _stream_worker(self):
        """流媒体工作线程 - 优化版本"""
        frame_interval = 1.0 / self.fps
        frame_count = 0
        last_log_time = time.time()

        # 预导入模块，避免重复导入
        from PIL import Image
        import io

        while self.is_streaming:
            try:
                start_time = time.time()

                # 使用adb screencap获取屏幕截图
                cmd = [
                    'adb', '-s', self.device_id, 'exec-out',
                    'screencap', '-p'
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    timeout=3  # 减少超时时间，提高响应速度
                )

                if result.returncode == 0 and result.stdout:
                    # 快速压缩图片
                    try:
                        # 将PNG数据转换为PIL Image
                        image = Image.open(io.BytesIO(result.stdout))

                        # 根据模式选择不同的处理方式
                        if hasattr(self, 'mode') and self.mode == 'ffmpeg':
                            # FFmpeg模式：使用更高质量的处理
                            if self.scale != 1.0:
                                new_width = int(image.width * self.scale)
                                new_height = int(image.height * self.scale)
                                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)  # 高质量缩放

                            # 转换为JPEG（FFmpeg模式：更高质量）
                            jpeg_buffer = io.BytesIO()
                            image.convert('RGB').save(
                                jpeg_buffer,
                                format='JPEG',
                                quality=max(70, self.quality),  # 更高质量
                                optimize=True  # 启用优化
                            )
                        else:
                            # 传统模式：使用更快的处理
                            if self.scale != 1.0:
                                new_width = int(image.width * self.scale)
                                new_height = int(image.height * self.scale)
                                image = image.resize((new_width, new_height), Image.Resampling.NEAREST)  # 使用更快的算法

                            # 转换为JPEG（传统模式：降低质量，提高速度）
                            jpeg_buffer = io.BytesIO()
                            image.convert('RGB').save(
                                jpeg_buffer,
                                format='JPEG',
                                quality=max(30, self.quality - 20),  # 降低质量提高速度
                                optimize=False  # 关闭优化，提高速度
                            )
                        jpeg_data = jpeg_buffer.getvalue()

                        # 转换为base64
                        image_data = base64.b64encode(jpeg_data).decode('utf-8')
                        format_type = 'jpeg'

                    except Exception as e:
                        # 如果压缩失败，使用原始PNG数据
                        image_data = base64.b64encode(result.stdout).decode('utf-8')
                        format_type = 'png'

                    # 发送到WebSocket客户端
                    message = {
                        'type': 'frame',
                        'data': image_data,
                        'timestamp': time.time(),
                        'format': format_type
                    }

                    # 存储最新帧数据，供WebSocket端点获取
                    self.latest_frame = message

                    # 减少日志输出频率（每5秒输出一次状态）
                    frame_count += 1
                    current_time = time.time()
                    if current_time - last_log_time >= 5.0:
                        logger.info(f"流媒体状态: {frame_count}帧/5秒, 数据大小: {len(image_data)}字符")
                        frame_count = 0
                        last_log_time = current_time

                # 控制帧率（优化：更精确的时间控制）
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)

            except subprocess.TimeoutExpired:
                # 减少超时日志
                pass
            except Exception as e:
                logger.error(f"流媒体工作线程错误: {e}")
                time.sleep(0.05)  # 减少错误恢复时间
    
    def start_recording(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """开始录制视频"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}

        if self.is_recording:
            return {"success": False, "message": "录制已在进行中"}

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screen_record_{timestamp}.mp4"

        filepath = os.path.join(self.recording_dir, filename)

        try:
            # 使用adb screenrecord录制视频，添加更多参数优化
            cmd = [
                'adb', '-s', self.device_id, 'shell',
                'screenrecord',
                '--bit-rate', '8000000',  # 8Mbps比特率
                '--time-limit', '180',    # 最大3分钟
                '--verbose',
                f'/sdcard/{filename}'
            ]

            self.record_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None  # 创建新的进程组
            )

            self.is_recording = True
            self.current_recording_file = filename

            logger.info(f"开始录制视频: {filename}")
            return {
                "success": True,
                "message": "录制已开始",
                "filename": filename,
                "filepath": filepath
            }

        except Exception as e:
            logger.error(f"开始录制失败: {e}")
            return {"success": False, "message": f"录制失败: {str(e)}"}
    
    def stop_recording(self) -> Dict[str, Any]:
        """停止录制视频"""
        if not self.is_recording:
            return {"success": False, "message": "当前没有录制任务"}

        try:
            if self.record_process:
                # 正确停止录制进程
                try:
                    # 首先尝试发送SIGINT信号（相当于Ctrl+C）
                    if hasattr(os, 'killpg'):
                        os.killpg(os.getpgid(self.record_process.pid), signal.SIGINT)
                    else:
                        self.record_process.send_signal(signal.SIGINT)

                    # 等待进程正常结束
                    self.record_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 如果5秒后还没结束，强制终止
                    logger.warning("录制进程未正常结束，强制终止")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)
                except Exception as e:
                    logger.warning(f"停止录制进程时出错: {e}")
                    self.record_process.terminate()
                    self.record_process.wait(timeout=5)

            # 等待一下确保文件写入完成
            time.sleep(2)

            # 从设备拉取录制的文件
            local_path = os.path.join(self.recording_dir, self.current_recording_file)
            device_path = f"/sdcard/{self.current_recording_file}"

            # 检查设备上文件是否存在
            check_cmd = ['adb', '-s', self.device_id, 'shell', 'ls', '-la', device_path]
            check_result = subprocess.run(check_cmd, capture_output=True, timeout=10)

            if check_result.returncode != 0:
                raise Exception(f"设备上录制文件不存在: {device_path}")

            pull_cmd = ['adb', '-s', self.device_id, 'pull', device_path, local_path]
            result = subprocess.run(pull_cmd, capture_output=True, timeout=60)

            if result.returncode == 0:
                # 删除设备上的文件
                rm_cmd = ['adb', '-s', self.device_id, 'shell', 'rm', device_path]
                subprocess.run(rm_cmd, timeout=5)

                self.is_recording = False
                self.record_process = None

                logger.info(f"录制完成: {self.current_recording_file}")
                return {
                    "success": True,
                    "message": "录制完成",
                    "filename": self.current_recording_file,
                    "filepath": local_path
                }
            else:
                error_msg = result.stderr.decode() if result.stderr else "未知错误"
                raise Exception(f"拉取录制文件失败: {error_msg}")

        except Exception as e:
            logger.error(f"停止录制失败: {e}")
            self.is_recording = False
            self.record_process = None
            return {"success": False, "message": f"停止录制失败: {str(e)}"}
    
    def take_screenshot(self, filename: Optional[str] = None) -> Dict[str, Any]:
        """截图当前屏幕"""
        if not self.device_id:
            return {"success": False, "message": "未设置设备ID"}
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"streaming_screenshot_{timestamp}.png"
        
        filepath = os.path.join(self.screenshots_dir, filename)
        
        try:
            # 使用adb screencap截图
            cmd = ['adb', '-s', self.device_id, 'exec-out', 'screencap', '-p']
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            
            if result.returncode == 0 and result.stdout:
                with open(filepath, 'wb') as f:
                    f.write(result.stdout)
                
                logger.info(f"截图完成: {filename}")
                return {
                    "success": True,
                    "message": "截图完成",
                    "filename": filename,
                    "filepath": filepath
                }
            else:
                raise Exception("截图命令执行失败")
                
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return {"success": False, "message": f"截图失败: {str(e)}"}
    
    def get_status(self) -> Dict[str, Any]:
        """获取流媒体状态"""
        return {
            "device_id": self.device_id,
            "is_streaming": self.is_streaming,
            "is_recording": self.is_recording,
            "connected_clients": len(self.websocket_clients),
            "fps": self.fps,
            "quality": self.quality,
            "scale": self.scale
        }
    
    def cleanup(self):
        """清理资源"""
        self.stop_streaming()
        if self.is_recording:
            self.stop_recording()
        self.websocket_clients.clear()

# 全局实例
screen_streaming_service = ScreenStreamingService()
